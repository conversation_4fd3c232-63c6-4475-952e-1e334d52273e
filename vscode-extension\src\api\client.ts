import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ConfigurationManager } from '../config/manager';
import { AuthenticationManager } from '../auth/manager';
import { SecurityIssue, ScanResult } from '../security/analyzer';

export interface ScanRequest {
    project_path: string;
    chains: string[];
    scan_type: string;
}

export interface ScanResponse {
    scan_id: string;
    status: string;
    message: string;
}

export interface FileScanResponse {
    issues: SecurityIssue[];
    file_path: string;
    scan_time: string;
}

export interface HealthResponse {
    status: string;
    version: string;
    timestamp: string;
    services: {
        api: string;
        database: string;
        scanner: string;
        websocket?: string;
        clients?: number;
    };
}

export interface ReportRequest {
    scan_id: string;
    format: string;
    include_details?: boolean;
}

export interface ReportResponse {
    report_id: string;
    download_url: string;
    format: string;
    generated_at: string;
}

export class SPTApiClient {
    private client: AxiosInstance;
    private baseUrl: string;
    private authManager?: AuthenticationManager;

    constructor(private configManager: ConfigurationManager) {
        this.baseUrl = this.configManager.getConfig('serverUrl') as string;

        this.client = axios.create({
            baseURL: this.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'SPT-VSCode-Extension/1.0.0'
            }
        });

        // Add request interceptor for authentication
        this.client.interceptors.request.use((config) => {
            const token = this.authManager?.getToken();
            if (token) {
                config.headers['Authorization'] = `Bearer ${token}`;
            }
            return config;
        });

        // Add response interceptor for error handling
        this.client.interceptors.response.use(
            (response) => response,
            async (error) => {
                if (error.response?.status === 401) {
                    // Handle unauthorized access - trigger re-authentication
                    if (this.authManager) {
                        const success = await this.authManager.authenticate();
                        if (success) {
                            // Retry the original request
                            const token = this.authManager.getToken();
                            if (token) {
                                error.config.headers['Authorization'] = `Bearer ${token}`;
                                return this.client.request(error.config);
                            }
                        }
                    }
                }
                console.error('SPT API Error:', error.response?.data || error.message);
                return Promise.reject(error);
            }
        );
    }

    /**
     * Set the authentication manager
     */
    setAuthManager(authManager: AuthenticationManager): void {
        this.authManager = authManager;
    }

    async healthCheck(): Promise<HealthResponse> {
        try {
            const response: AxiosResponse<HealthResponse> = await this.client.get('/health');
            return response.data;
        } catch (error) {
            throw new Error(`Health check failed: ${error}`);
        }
    }

    async startScan(request: ScanRequest): Promise<ScanResponse> {
        try {
            const response: AxiosResponse<ScanResponse> = await this.client.post('/api/v1/scan/start', request);
            return response.data;
        } catch (error) {
            throw new Error(`Failed to start scan: ${error}`);
        }
    }

    async getScanResult(scanId: string): Promise<ScanResult> {
        try {
            const response: AxiosResponse<ScanResult> = await this.client.get(`/api/v1/scan/result/${scanId}`);
            return response.data;
        } catch (error) {
            throw new Error(`Failed to get scan result: ${error}`);
        }
    }

    async getScanHistory(limit?: number): Promise<ScanResult[]> {
        try {
            const params = limit ? { limit } : {};
            const response: AxiosResponse<ScanResult[]> = await this.client.get('/api/v1/scan/history', { params });
            return response.data;
        } catch (error) {
            throw new Error(`Failed to get scan history: ${error}`);
        }
    }

    async scanFile(filePath: string, chains: string[]): Promise<FileScanResponse> {
        try {
            const params = {
                file: filePath,
                chains: chains.join(',')
            };
            const response: AxiosResponse<FileScanResponse> = await this.client.get('/api/v1/scan/file', { params });
            return response.data;
        } catch (error) {
            throw new Error(`Failed to scan file: ${error}`);
        }
    }

    async generateReport(request: ReportRequest): Promise<ReportResponse> {
        try {
            const response: AxiosResponse<ReportResponse> = await this.client.post('/api/v1/report/generate', request);
            return response.data;
        } catch (error) {
            throw new Error(`Failed to generate report: ${error}`);
        }
    }

    async getSecurityChecklist(chain?: string, category?: string): Promise<any[]> {
        try {
            const params: any = {};
            if (chain) {
                params.chain = chain;
            }
            if (category) {
                params.category = category;
            }
            
            const response: AxiosResponse<any[]> = await this.client.get('/api/v1/checklist', { params });
            return response.data;
        } catch (error) {
            throw new Error(`Failed to get security checklist: ${error}`);
        }
    }

    async getConfiguration(): Promise<any> {
        try {
            const response: AxiosResponse<any> = await this.client.get('/api/v1/config');
            return response.data;
        } catch (error) {
            throw new Error(`Failed to get configuration: ${error}`);
        }
    }

    async updateConfiguration(config: any): Promise<any> {
        try {
            const response: AxiosResponse<any> = await this.client.put('/api/v1/config', config);
            return response.data;
        } catch (error) {
            throw new Error(`Failed to update configuration: ${error}`);
        }
    }

    // Update client configuration when settings change
    updateConfig(): void {
        this.baseUrl = this.configManager.getConfig('serverUrl') as string;
        this.client.defaults.baseURL = this.baseUrl;
    }

    // Test connection to SPT backend
    async testConnection(): Promise<boolean> {
        try {
            await this.healthCheck();
            return true;
        } catch (error) {
            return false;
        }
    }

    // Check if user is authenticated
    isAuthenticated(): boolean {
        return this.authManager?.isAuthenticated() ?? false;
    }

    // Ensure user is authenticated before making requests
    async ensureAuthenticated(): Promise<boolean> {
        if (this.isAuthenticated()) {
            return true;
        }

        if (this.authManager) {
            return await this.authManager.authenticate();
        }

        return false;
    }

    // Get server status and information
    async getServerInfo(): Promise<{
        connected: boolean;
        version?: string;
        services?: any;
        error?: string;
    }> {
        try {
            const health = await this.healthCheck();
            return {
                connected: true,
                version: health.version,
                services: health.services
            };
        } catch (error) {
            return {
                connected: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    // Cancel a running scan
    async cancelScan(scanId: string): Promise<void> {
        try {
            await this.client.post(`/api/v1/scan/${scanId}/cancel`);
        } catch (error) {
            throw new Error(`Failed to cancel scan: ${error}`);
        }
    }

    // Get scan statistics
    async getScanStatistics(): Promise<any> {
        try {
            const response: AxiosResponse<any> = await this.client.get('/api/v1/scan/statistics');
            return response.data;
        } catch (error) {
            throw new Error(`Failed to get scan statistics: ${error}`);
        }
    }

    // Export scan results
    async exportScanResults(scanId: string, format: string): Promise<Blob> {
        try {
            const response = await this.client.get(`/api/v1/scan/${scanId}/export`, {
                params: { format },
                responseType: 'blob'
            });
            return response.data;
        } catch (error) {
            throw new Error(`Failed to export scan results: ${error}`);
        }
    }
}
