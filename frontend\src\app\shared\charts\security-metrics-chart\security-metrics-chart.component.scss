/* Security Metrics Chart Component Styles */

.chart-container {
  background: var(--spt-surface);
  border-radius: var(--spt-radius-xl);
  border: 1px solid var(--spt-border);
  box-shadow: var(--spt-shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-container:hover {
  box-shadow: var(--spt-shadow-md);
  transform: translateY(-1px);
}

/* Chart Header */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spt-space-6);
  border-bottom: 1px solid var(--spt-border-light);
  background: var(--spt-bg-secondary);
}

.chart-title-section {
  flex: 1;
}

.chart-title {
  margin: 0 0 var(--spt-space-1) 0;
  font-size: var(--spt-text-lg);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
  line-height: var(--spt-leading-tight);
}

.chart-subtitle {
  font-size: var(--spt-text-sm);
  color: var(--spt-text-secondary);
  font-weight: var(--spt-font-medium);
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: var(--spt-space-3);
}

.time-range-select {
  min-width: 140px;
  
  ::ng-deep .mat-mdc-form-field-wrapper {
    padding-bottom: 0;
  }
  
  ::ng-deep .mat-mdc-text-field-wrapper {
    height: 40px;
    border-radius: var(--spt-radius-lg);
  }
}

.chart-actions {
  display: flex;
  gap: var(--spt-space-1);
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: var(--spt-radius-lg);
  color: var(--spt-text-secondary);
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--spt-primary-50);
  color: var(--spt-primary-600);
  transform: scale(1.05);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Chart Content */
.chart-content {
  position: relative;
  padding: var(--spt-space-6);
  min-height: 300px;
}

.chart-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
}

.chart-canvas {
  max-width: 100%;
  max-height: 100%;
}

/* Loading State */
.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--spt-text-secondary);
}

.loading-spinner {
  margin-bottom: var(--spt-space-4);
}

.loading-spinner mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: var(--spt-primary-500);
}

/* Empty State */
.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--spt-text-secondary);
}

.empty-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: var(--spt-text-tertiary);
  margin-bottom: var(--spt-space-4);
}

.chart-empty h4 {
  margin: 0 0 var(--spt-space-2) 0;
  font-size: var(--spt-text-lg);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.chart-empty p {
  margin: 0 0 var(--spt-space-6) 0;
  font-size: var(--spt-text-sm);
  max-width: 300px;
  line-height: var(--spt-leading-relaxed);
}

/* Chart Trends */
.chart-trends {
  padding: var(--spt-space-6);
  border-top: 1px solid var(--spt-border-light);
  background: var(--spt-bg-secondary);
}

.trends-header {
  display: flex;
  align-items: center;
  gap: var(--spt-space-2);
  margin-bottom: var(--spt-space-4);
  font-size: var(--spt-text-sm);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.trends-header mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: var(--spt-primary-600);
}

.trends-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spt-space-3);
}

.trend-item {
  display: flex;
  align-items: center;
  gap: var(--spt-space-3);
  padding: var(--spt-space-3);
  border-radius: var(--spt-radius-lg);
  background: var(--spt-surface);
  border: 1px solid var(--spt-border-light);
  transition: all 0.2s ease;
}

.trend-item:hover {
  transform: translateY(-1px);
  box-shadow: var(--spt-shadow-sm);
}

.trend-indicator {
  width: 12px;
  height: 12px;
  border-radius: var(--spt-radius-full);
  flex-shrink: 0;
}

.trend-content {
  flex: 1;
  min-width: 0;
}

.trend-label {
  display: block;
  font-size: var(--spt-text-sm);
  font-weight: var(--spt-font-medium);
  color: var(--spt-text-primary);
  margin-bottom: var(--spt-space-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.trend-value {
  display: flex;
  align-items: center;
  gap: var(--spt-space-2);
}

.value {
  font-size: var(--spt-text-lg);
  font-weight: var(--spt-font-bold);
  color: var(--spt-text-primary);
}

.trend-change {
  display: flex;
  align-items: center;
  gap: var(--spt-space-1);
  font-size: var(--spt-text-xs);
  font-weight: var(--spt-font-semibold);
  padding: var(--spt-space-1) var(--spt-space-2);
  border-radius: var(--spt-radius-md);
}

.trend-change.positive {
  background: var(--spt-error-100);
  color: var(--spt-error-700);
}

.trend-change.negative {
  background: var(--spt-success-100);
  color: var(--spt-success-700);
}

.trend-change.neutral {
  background: var(--spt-gray-100);
  color: var(--spt-text-secondary);
}

.trend-icon {
  font-size: 14px;
  width: 14px;
  height: 14px;
}

/* Chart Summary */
.chart-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spt-space-6);
  border-top: 1px solid var(--spt-border-light);
  background: var(--spt-bg-secondary);
}

.summary-stats {
  display: flex;
  gap: var(--spt-space-6);
}

.summary-item {
  display: flex;
  align-items: center;
  gap: var(--spt-space-2);
  padding: var(--spt-space-2) var(--spt-space-3);
  border-radius: var(--spt-radius-lg);
  background: var(--spt-surface);
  border: 1px solid var(--spt-border-light);
}

.summary-item mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.summary-item.critical mat-icon { color: var(--spt-error-600); }
.summary-item.high mat-icon { color: var(--spt-warning-600); }
.summary-item.medium mat-icon { color: var(--spt-info-600); }
.summary-item.low mat-icon { color: var(--spt-success-600); }

.count {
  font-size: var(--spt-text-lg);
  font-weight: var(--spt-font-bold);
  color: var(--spt-text-primary);
}

.label {
  font-size: var(--spt-text-sm);
  font-weight: var(--spt-font-medium);
  color: var(--spt-text-secondary);
}

.summary-actions {
  display: flex;
  gap: var(--spt-space-2);
}

.view-details-btn {
  border-radius: var(--spt-radius-lg);
  font-weight: var(--spt-font-medium);
}

/* Animations */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: var(--spt-space-4);
    align-items: stretch;
  }

  .chart-controls {
    justify-content: space-between;
  }

  .trends-list {
    grid-template-columns: 1fr;
  }

  .chart-summary {
    flex-direction: column;
    gap: var(--spt-space-4);
    align-items: stretch;
  }

  .summary-stats {
    justify-content: space-around;
    flex-wrap: wrap;
  }
}
