/* Scan Progress Chart Component Styles */

.progress-container {
  background: var(--spt-surface);
  border-radius: var(--spt-radius-xl);
  border: 1px solid var(--spt-border);
  box-shadow: var(--spt-shadow-sm);
  overflow: hidden;
}

/* Progress Header */
.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spt-space-6);
  border-bottom: 1px solid var(--spt-border-light);
  background: var(--spt-bg-secondary);
}

.scan-info {
  flex: 1;
}

.scan-title {
  display: flex;
  align-items: center;
  gap: var(--spt-space-3);
  margin-bottom: var(--spt-space-2);
}

.status-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.scan-title h3 {
  margin: 0;
  font-size: var(--spt-text-lg);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.scan-id {
  font-size: var(--spt-text-xs);
  color: var(--spt-text-tertiary);
  background: var(--spt-gray-100);
  padding: var(--spt-space-1) var(--spt-space-2);
  border-radius: var(--spt-radius-md);
  font-family: var(--spt-font-mono);
}

.scan-status {
  display: flex;
  flex-direction: column;
  gap: var(--spt-space-1);
}

.status-text {
  font-size: var(--spt-text-sm);
  font-weight: var(--spt-font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.scan-status.status-pending .status-text { color: var(--spt-warning-600); }
.scan-status.status-running .status-text { color: var(--spt-info-600); }
.scan-status.status-completed .status-text { color: var(--spt-success-600); }
.scan-status.status-failed .status-text { color: var(--spt-error-600); }

.current-step {
  font-size: var(--spt-text-xs);
  color: var(--spt-text-secondary);
}

.progress-actions {
  display: flex;
  gap: var(--spt-space-2);
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: var(--spt-radius-lg);
  transition: all 0.2s ease;
}

.action-btn.pause {
  color: var(--spt-warning-600);
}

.action-btn.pause:hover {
  background: var(--spt-warning-50);
  color: var(--spt-warning-700);
}

.action-btn.cancel {
  color: var(--spt-error-600);
}

.action-btn.cancel:hover {
  background: var(--spt-error-50);
  color: var(--spt-error-700);
}

/* Main Progress */
.main-progress {
  padding: var(--spt-space-6);
  border-bottom: 1px solid var(--spt-border-light);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spt-space-3);
}

.progress-label {
  font-size: var(--spt-text-base);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.progress-percentage {
  font-size: var(--spt-text-2xl);
  font-weight: var(--spt-font-bold);
  color: var(--spt-primary-600);
}

.main-progress-bar {
  height: 12px;
  border-radius: var(--spt-radius-lg);
  margin-bottom: var(--spt-space-3);
  
  ::ng-deep .mat-mdc-progress-bar-fill::after {
    border-radius: var(--spt-radius-lg);
  }
  
  ::ng-deep .mat-mdc-progress-bar-buffer {
    border-radius: var(--spt-radius-lg);
  }
}

.progress-details {
  display: flex;
  justify-content: space-between;
  font-size: var(--spt-text-sm);
  color: var(--spt-text-secondary);
}

/* Progress Stats Grid */
.progress-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spt-space-4);
  padding: var(--spt-space-6);
  border-bottom: 1px solid var(--spt-border-light);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spt-space-3);
  padding: var(--spt-space-4);
  background: var(--spt-bg-secondary);
  border-radius: var(--spt-radius-lg);
  border: 1px solid var(--spt-border-light);
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--spt-shadow-sm);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--spt-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-card.files .stat-icon {
  background: var(--spt-info-100);
  color: var(--spt-info-600);
}

.stat-card.issues .stat-icon {
  background: var(--spt-error-100);
  color: var(--spt-error-600);
}

.stat-card.time .stat-icon {
  background: var(--spt-warning-100);
  color: var(--spt-warning-600);
}

.stat-card.speed .stat-icon {
  background: var(--spt-success-100);
  color: var(--spt-success-600);
}

.stat-icon mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: var(--spt-text-xl);
  font-weight: var(--spt-font-bold);
  color: var(--spt-text-primary);
  line-height: var(--spt-leading-tight);
}

.stat-label {
  font-size: var(--spt-text-sm);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
  margin: var(--spt-space-1) 0;
}

.stat-detail {
  font-size: var(--spt-text-xs);
  color: var(--spt-text-secondary);
}

/* Progress Chart */
.progress-chart {
  padding: var(--spt-space-6);
  border-bottom: 1px solid var(--spt-border-light);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spt-space-4);
}

.chart-header h4 {
  margin: 0;
  font-size: var(--spt-text-base);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.chart-legend {
  display: flex;
  gap: var(--spt-space-4);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spt-space-2);
  font-size: var(--spt-text-sm);
  color: var(--spt-text-secondary);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: var(--spt-radius-sm);
}

.legend-color.progress {
  background: #3b82f6;
}

.legend-color.issues {
  background: #ef4444;
}

.chart-content {
  height: 200px;
  position: relative;
}

.progress-canvas {
  max-width: 100%;
  max-height: 100%;
}

/* Step Progress */
.step-progress {
  padding: var(--spt-space-6);
  border-bottom: 1px solid var(--spt-border-light);
}

.step-progress h4 {
  margin: 0 0 var(--spt-space-4) 0;
  font-size: var(--spt-text-base);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: var(--spt-space-3);
}

.step-item {
  display: flex;
  align-items: center;
  gap: var(--spt-space-4);
  padding: var(--spt-space-3);
  border-radius: var(--spt-radius-lg);
  transition: all 0.2s ease;
}

.step-item.completed {
  background: var(--spt-success-50);
  border: 1px solid var(--spt-success-200);
}

.step-item.current {
  background: var(--spt-info-50);
  border: 1px solid var(--spt-info-200);
}

.step-item.pending {
  background: var(--spt-gray-50);
  border: 1px solid var(--spt-border-light);
}

.step-indicator {
  width: 32px;
  height: 32px;
  border-radius: var(--spt-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--spt-font-semibold);
  font-size: var(--spt-text-sm);
  flex-shrink: 0;
}

.step-item.completed .step-indicator {
  background: var(--spt-success-600);
  color: white;
}

.step-item.current .step-indicator {
  background: var(--spt-info-600);
  color: white;
}

.step-item.pending .step-indicator {
  background: var(--spt-gray-200);
  color: var(--spt-text-secondary);
}

.step-content {
  flex: 1;
  min-width: 0;
}

.step-name {
  font-size: var(--spt-text-sm);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
  margin-bottom: var(--spt-space-1);
}

.step-description {
  font-size: var(--spt-text-xs);
  color: var(--spt-text-secondary);
}

.step-status {
  width: 100px;
  display: flex;
  justify-content: flex-end;
}

.completed-icon {
  color: var(--spt-success-600);
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.step-progress-bar {
  width: 80px;
  height: 4px;
}

/* Completion Summary */
.completion-summary {
  padding: var(--spt-space-6);
}

.summary-header {
  display: flex;
  align-items: center;
  gap: var(--spt-space-3);
  margin-bottom: var(--spt-space-4);
}

.summary-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
}

.summary-header h4 {
  margin: 0;
  font-size: var(--spt-text-lg);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spt-space-4);
  margin-bottom: var(--spt-space-6);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spt-space-3);
  background: var(--spt-bg-secondary);
  border-radius: var(--spt-radius-lg);
  border: 1px solid var(--spt-border-light);
}

.summary-item .label {
  font-size: var(--spt-text-sm);
  color: var(--spt-text-secondary);
}

.summary-item .value {
  font-size: var(--spt-text-base);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.summary-actions {
  display: flex;
  gap: var(--spt-space-3);
  justify-content: center;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spt-space-12);
  text-align: center;
  color: var(--spt-text-secondary);
}

.empty-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: var(--spt-text-tertiary);
  margin-bottom: var(--spt-space-4);
}

.empty-state h4 {
  margin: 0 0 var(--spt-space-2) 0;
  font-size: var(--spt-text-lg);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.empty-state p {
  margin: 0 0 var(--spt-space-6) 0;
  font-size: var(--spt-text-sm);
  max-width: 300px;
  line-height: var(--spt-leading-relaxed);
}

/* Animations */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .progress-header {
    flex-direction: column;
    gap: var(--spt-space-4);
    align-items: stretch;
  }

  .progress-stats {
    grid-template-columns: 1fr;
  }

  .chart-header {
    flex-direction: column;
    gap: var(--spt-space-2);
    align-items: stretch;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .summary-actions {
    flex-direction: column;
  }
}
