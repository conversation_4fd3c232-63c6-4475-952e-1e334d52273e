<div class="trends-dashboard">
  <!-- Dashboard Header -->
  <div class="dashboard-header">
    <div class="header-content">
      <h2 class="dashboard-title">
        <mat-icon>trending_up</mat-icon>
        Security Trends Analysis
      </h2>
      <p class="dashboard-subtitle">Monitor security metrics and trends over time</p>
    </div>

    <div class="header-controls">
      <!-- Period Selector -->
      <mat-form-field appearance="outline" class="period-select">
        <mat-label>Time Period</mat-label>
        <mat-select 
          [(value)]="selectedPeriod" 
          (selectionChange)="onPeriodChange($event.value)">
          <mat-option *ngFor="let period of periods" [value]="period.value">
            {{ period.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <!-- Actions -->
      <div class="header-actions">
        <button 
          mat-icon-button 
          matTooltip="Refresh Data"
          (click)="refreshData()"
          class="action-btn"
          [disabled]="isLoading">
          <mat-icon [class.spinning]="isLoading">refresh</mat-icon>
        </button>
        
        <button 
          mat-icon-button 
          matTooltip="Export Chart"
          (click)="exportChart()"
          class="action-btn">
          <mat-icon>download</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Summary Cards -->
  <div class="summary-cards" *ngIf="trendsData">
    <div class="summary-card total-issues">
      <div class="card-header">
        <mat-icon>bug_report</mat-icon>
        <span class="card-title">Total Issues</span>
      </div>
      <div class="card-content">
        <div class="metric-value">{{ trendsData.summary.totalIssues }}</div>
        <div class="metric-trend" 
             [style.color]="getTrendColor(getTrendDirection(trendsData.summary.criticalTrend), false)">
          <mat-icon>{{ getTrendIcon(getTrendDirection(trendsData.summary.criticalTrend)) }}</mat-icon>
          <span>{{ Math.abs(trendsData.summary.criticalTrend) }}%</span>
        </div>
      </div>
    </div>

    <div class="summary-card security-score">
      <div class="card-header">
        <mat-icon>grade</mat-icon>
        <span class="card-title">Avg Security Score</span>
      </div>
      <div class="card-content">
        <div class="metric-value">{{ trendsData.summary.averageScore }}/100</div>
        <div class="score-bar">
          <div class="score-fill" [style.width.%]="trendsData.summary.averageScore"></div>
        </div>
      </div>
    </div>

    <div class="summary-card scan-frequency">
      <div class="card-header">
        <mat-icon>schedule</mat-icon>
        <span class="card-title">Scan Frequency</span>
      </div>
      <div class="card-content">
        <div class="metric-value">{{ trendsData.summary.scanFrequency }}</div>
        <div class="metric-label">scans per day</div>
      </div>
    </div>

    <div class="summary-card trend-indicator">
      <div class="card-header">
        <mat-icon>insights</mat-icon>
        <span class="card-title">Overall Trend</span>
      </div>
      <div class="card-content">
        <div class="trend-status" 
             [class]="'trend-' + getTrendDirection(trendsData.summary.criticalTrend)">
          <mat-icon>{{ getTrendIcon(getTrendDirection(trendsData.summary.criticalTrend)) }}</mat-icon>
          <span>{{ getTrendDirection(trendsData.summary.criticalTrend) === 'up' ? 'Increasing' : 
                    getTrendDirection(trendsData.summary.criticalTrend) === 'down' ? 'Improving' : 'Stable' }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Chart Tabs -->
  <div class="chart-section">
    <mat-tab-group 
      [(selectedIndex)]="selectedTabIndex" 
      (selectedTabChange)="onViewChange(chartViews[$event.index].value)"
      class="chart-tabs">
      
      <!-- Security Trends Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>trending_up</mat-icon>
          <span>Security Trends</span>
        </ng-template>
        
        <div class="chart-container">
          <div class="chart-header">
            <h3>Security Issues Over Time</h3>
            <p>Track the evolution of security issues by severity level</p>
          </div>
          
          <div class="chart-content" [class.loading]="isLoading">
            <div *ngIf="isLoading" class="chart-loading">
              <mat-icon class="spinning">refresh</mat-icon>
              <span>Loading trends data...</span>
            </div>
            
            <canvas 
              #trendsCanvas
              *ngIf="!isLoading && trendsData"
              class="chart-canvas"
              [attr.aria-label]="'Security trends chart for ' + selectedPeriod">
            </canvas>
            
            <div *ngIf="!isLoading && !trendsData" class="chart-empty">
              <mat-icon>trending_up</mat-icon>
              <h4>No Trends Data</h4>
              <p>No security trends data available for the selected period.</p>
            </div>
          </div>
        </div>
      </mat-tab>

      <!-- Security Scores Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>grade</mat-icon>
          <span>Security Scores</span>
        </ng-template>
        
        <div class="chart-container">
          <div class="chart-header">
            <h3>Security Score History</h3>
            <p>Monitor overall security score improvements over time</p>
          </div>
          
          <div class="chart-content" [class.loading]="isLoading">
            <div *ngIf="isLoading" class="chart-loading">
              <mat-icon class="spinning">refresh</mat-icon>
              <span>Loading score data...</span>
            </div>
            
            <canvas 
              #scoreCanvas
              *ngIf="!isLoading && trendsData"
              class="chart-canvas"
              [attr.aria-label]="'Security scores chart for ' + selectedPeriod">
            </canvas>
            
            <div *ngIf="!isLoading && !trendsData" class="chart-empty">
              <mat-icon>grade</mat-icon>
              <h4>No Score Data</h4>
              <p>No security score data available for the selected period.</p>
            </div>
          </div>
        </div>
      </mat-tab>

      <!-- Scan Volume Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>bar_chart</mat-icon>
          <span>Scan Volume</span>
        </ng-template>
        
        <div class="chart-container">
          <div class="chart-header">
            <h3>Scan Activity Volume</h3>
            <p>Track scanning frequency and activity patterns</p>
          </div>
          
          <div class="chart-content" [class.loading]="isLoading">
            <div *ngIf="isLoading" class="chart-loading">
              <mat-icon class="spinning">refresh</mat-icon>
              <span>Loading volume data...</span>
            </div>
            
            <canvas 
              #volumeCanvas
              *ngIf="!isLoading && trendsData"
              class="chart-canvas"
              [attr.aria-label]="'Scan volume chart for ' + selectedPeriod">
            </canvas>
            
            <div *ngIf="!isLoading && !trendsData" class="chart-empty">
              <mat-icon>bar_chart</mat-icon>
              <h4>No Volume Data</h4>
              <p>No scan volume data available for the selected period.</p>
            </div>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>

  <!-- Insights Panel -->
  <div class="insights-panel" *ngIf="trendsData">
    <div class="insights-header">
      <mat-icon>lightbulb</mat-icon>
      <h3>Key Insights</h3>
    </div>
    
    <div class="insights-content">
      <div class="insight-item" *ngIf="trendsData.summary.criticalTrend > 10">
        <mat-icon class="insight-icon warning">warning</mat-icon>
        <div class="insight-text">
          <strong>Critical issues are increasing</strong> by {{ trendsData.summary.criticalTrend }}%. 
          Consider reviewing security practices and increasing scan frequency.
        </div>
      </div>
      
      <div class="insight-item" *ngIf="trendsData.summary.criticalTrend < -10">
        <mat-icon class="insight-icon success">check_circle</mat-icon>
        <div class="insight-text">
          <strong>Security is improving!</strong> Critical issues decreased by {{ Math.abs(trendsData.summary.criticalTrend) }}%. 
          Keep up the good security practices.
        </div>
      </div>
      
      <div class="insight-item" *ngIf="trendsData.summary.averageScore < 60">
        <mat-icon class="insight-icon error">error</mat-icon>
        <div class="insight-text">
          <strong>Low security score detected.</strong> Average score is {{ trendsData.summary.averageScore }}/100. 
          Focus on addressing high and critical severity issues.
        </div>
      </div>
      
      <div class="insight-item" *ngIf="trendsData.summary.scanFrequency < 1">
        <mat-icon class="insight-icon info">info</mat-icon>
        <div class="insight-text">
          <strong>Low scan frequency.</strong> Consider increasing scan frequency to {{ trendsData.summary.scanFrequency }} per day 
          for better security monitoring.
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Empty State -->
<div class="empty-dashboard" *ngIf="!trendsData">
  <mat-icon class="empty-icon">trending_up</mat-icon>
  <h3>No Trends Data Available</h3>
  <p>Start running security scans to see trends and analytics here.</p>
  <button mat-raised-button color="primary">
    <mat-icon>security</mat-icon>
    Start First Scan
  </button>
</div>
