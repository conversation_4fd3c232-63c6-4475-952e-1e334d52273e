package dependencies

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/config"
	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
)

// Scanner represents the dependency security scanner
type Scanner struct {
	config             *config.Config
	logger             *logrus.Logger
	vulnerabilityDB    *VulnerabilityDatabase
	packageAnalyzers   map[string]PackageAnalyzer
	environmentScanner *EnvironmentScanner
	cicdScanner        *CICDScanner
}

// PackageAnalyzer interface for different package managers
type PackageAnalyzer interface {
	AnalyzePackageFile(filePath string, content string) ([]models.SecurityIssue, error)
	GetSupportedFiles() []string
	GetPackageManager() string
}

// DependencyInfo represents information about a dependency
type DependencyInfo struct {
	Name            string
	Version         string
	PackageManager  string
	FilePath        string
	IsDevDependency bool
	IsDirectDep     bool
	Vulnerabilities []Vulnerability
	License         string
	Deprecated      bool
	Outdated        bool
	RiskScore       float64
}

// Vulnerability represents a security vulnerability
type Vulnerability struct {
	ID          string
	Title       string
	Description string
	Severity    string
	CVSS        float64
	CVE         string
	CWE         string
	References  []string
	PatchedIn   string
	PublishedAt time.Time
}

// NewScanner creates a new dependency scanner instance
func NewScanner(cfg *config.Config) (*Scanner, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	scanner := &Scanner{
		config:           cfg,
		logger:           logrus.New(),
		vulnerabilityDB:  NewVulnerabilityDatabase(),
		packageAnalyzers: make(map[string]PackageAnalyzer),
	}

	// Configure logger level based on config
	if cfg.Logging.Level != "" {
		level, err := logrus.ParseLevel(cfg.Logging.Level)
		if err == nil {
			scanner.logger.SetLevel(level)
		}
	}

	// Initialize package analyzers directly
	scanner.packageAnalyzers["npm"] = NewNPMAnalyzer()
	scanner.packageAnalyzers["yarn"] = NewYarnAnalyzer()
	scanner.packageAnalyzers["pip"] = NewPipAnalyzer()
	scanner.packageAnalyzers["poetry"] = NewPoetryAnalyzer()
	scanner.packageAnalyzers["cargo"] = NewCargoAnalyzer()
	scanner.packageAnalyzers["go"] = NewGoAnalyzer()
	scanner.packageAnalyzers["composer"] = NewComposerAnalyzer()
	scanner.packageAnalyzers["maven"] = NewMavenAnalyzer()
	scanner.packageAnalyzers["gradle"] = NewGradleAnalyzer()
	scanner.packageAnalyzers["nuget"] = NewNuGetAnalyzer()

	// Initialize environment and CI/CD scanners
	scanner.environmentScanner = NewEnvironmentScanner()
	scanner.cicdScanner = NewCICDScanner()

	if scanner.environmentScanner == nil || scanner.cicdScanner == nil {
		return nil, fmt.Errorf("failed to initialize environment or CI/CD scanners")
	}

	// Validate configuration
	if err := scanner.validateConfig(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	scanner.logger.Infof("Dependency scanner initialized with %d package analyzers", len(scanner.packageAnalyzers))
	return scanner, nil
}

// validateConfig validates the scanner configuration
func (s *Scanner) validateConfig() error {
	if s.config.Security.Level == "" {
		s.logger.Warn("Security level not specified, using default 'medium'")
		s.config.Security.Level = "medium"
	}

	validLevels := []string{"low", "medium", "high", "strict"}
	isValid := false
	for _, level := range validLevels {
		if s.config.Security.Level == level {
			isValid = true
			break
		}
	}

	if !isValid {
		return fmt.Errorf("invalid security level '%s', must be one of: %v", s.config.Security.Level, validLevels)
	}

	return nil
}

// ScanProject scans an entire project for dependency and environment issues
func (s *Scanner) ScanProject(ctx context.Context, projectPath string) ([]models.SecurityIssue, error) {
	var allIssues []models.SecurityIssue

	s.logger.Infof("Starting dependency and environment scan for project: %s", projectPath)

	// Scan for package files
	packageIssues, err := s.scanPackageFiles(ctx, projectPath)
	if err != nil {
		s.logger.Errorf("Package file scanning failed: %v", err)
	} else {
		allIssues = append(allIssues, packageIssues...)
	}

	// Scan environment configuration
	envIssues, err := s.environmentScanner.ScanEnvironment(projectPath)
	if err != nil {
		s.logger.Errorf("Environment scanning failed: %v", err)
	} else {
		allIssues = append(allIssues, envIssues...)
	}

	// Scan CI/CD configurations
	cicdIssues, err := s.cicdScanner.ScanCICD(projectPath)
	if err != nil {
		s.logger.Errorf("CI/CD scanning failed: %v", err)
	} else {
		allIssues = append(allIssues, cicdIssues...)
	}

	s.logger.Infof("Dependency and environment scan completed. Found %d issues", len(allIssues))
	return allIssues, nil
}

// ScanFile scans a specific file for dependency issues
func (s *Scanner) ScanFile(ctx context.Context, filePath string) ([]models.SecurityIssue, error) {
	// Input validation
	if filePath == "" {
		return nil, fmt.Errorf("file path cannot be empty")
	}

	if ctx == nil {
		ctx = context.Background()
	}

	// Check if file exists and is readable
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to access file %s: %w", filePath, err)
	}

	if fileInfo.IsDir() {
		return nil, fmt.Errorf("path %s is a directory, not a file", filePath)
	}

	// Check file size to avoid processing extremely large files
	const maxFileSize = 10 * 1024 * 1024 // 10MB
	if fileInfo.Size() > maxFileSize {
		s.logger.Warnf("Skipping large file %s (size: %d bytes)", filePath, fileInfo.Size())
		return []models.SecurityIssue{}, nil
	}

	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}

	var issues []models.SecurityIssue
	fileName := filepath.Base(filePath)

	s.logger.Debugf("Scanning file: %s (size: %d bytes)", filePath, len(content))

	// Determine which analyzer to use based on file name
	for _, analyzer := range s.packageAnalyzers {
		for _, supportedFile := range analyzer.GetSupportedFiles() {
			if s.matchesPattern(fileName, supportedFile) {
				analyzerIssues, err := analyzer.AnalyzePackageFile(filePath, string(content))
				if err != nil {
					s.logger.Warnf("Analysis failed for %s with %s analyzer: %v",
						filePath, analyzer.GetPackageManager(), err)
					continue
				}
				issues = append(issues, analyzerIssues...)
				s.logger.Debugf("Found %d issues with %s analyzer", len(analyzerIssues), analyzer.GetPackageManager())
			}
		}
	}

	// Check for environment-related issues in the file
	if s.isEnvironmentFile(fileName) {
		envIssues, err := s.environmentScanner.ScanFile(filePath, string(content))
		if err != nil {
			s.logger.Warnf("Environment analysis failed for %s: %v", filePath, err)
		} else {
			issues = append(issues, envIssues...)
			s.logger.Debugf("Found %d environment issues", len(envIssues))
		}
	}

	// Check for CI/CD-related issues in the file
	if s.isCICDFile(fileName) {
		cicdIssues, err := s.cicdScanner.ScanFile(filePath, string(content))
		if err != nil {
			s.logger.Warnf("CI/CD analysis failed for %s: %v", filePath, err)
		} else {
			issues = append(issues, cicdIssues...)
			s.logger.Debugf("Found %d CI/CD issues", len(cicdIssues))
		}
	}

	s.logger.Debugf("Total issues found in %s: %d", filePath, len(issues))
	return issues, nil
}

// scanPackageFiles scans all package files in the project
func (s *Scanner) scanPackageFiles(ctx context.Context, projectPath string) ([]models.SecurityIssue, error) {
	var allIssues []models.SecurityIssue

	// Walk through the project directory
	err := filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip entire directories that should be excluded
		if info.IsDir() {
			if s.shouldSkipDirectory(info.Name(), path) {
				s.logger.Debugf("Skipping directory: %s", path)
				return filepath.SkipDir
			}
			return nil
		}

		// Skip hidden files
		if strings.HasPrefix(info.Name(), ".") {
			return nil
		}

		// Skip files in excluded paths
		if s.shouldSkipPath(path) {
			return nil
		}

		// Check if this is a package file
		if s.isPackageFile(info.Name()) {
			issues, err := s.ScanFile(ctx, path)
			if err != nil {
				s.logger.Warnf("Failed to scan package file %s: %v", path, err)
				return nil // Continue with other files
			}
			allIssues = append(allIssues, issues...)
		}

		return nil
	})

	return allIssues, err
}

// isPackageFile checks if a file is a package manager file
func (s *Scanner) isPackageFile(fileName string) bool {
	packageFiles := []string{
		"package.json", "package-lock.json", "yarn.lock",
		"requirements.txt", "Pipfile", "Pipfile.lock", "pyproject.toml",
		"Cargo.toml", "Cargo.lock",
		"go.mod", "go.sum",
		"composer.json", "composer.lock",
		"pom.xml", "build.gradle", "gradle.lockfile",
		"packages.config", "*.csproj", "*.fsproj", "*.vbproj",
		"Gemfile", "Gemfile.lock",
		"mix.exs", "mix.lock",
	}

	for _, pattern := range packageFiles {
		if matched, _ := filepath.Match(pattern, fileName); matched {
			return true
		}
		if strings.Contains(fileName, pattern) {
			return true
		}
	}

	return false
}

// isEnvironmentFile checks if a file is an environment configuration file
func (s *Scanner) isEnvironmentFile(fileName string) bool {
	envFiles := []string{
		".env", ".env.local", ".env.development", ".env.production",
		".env.test", ".env.staging", "config.json", "config.yaml",
		"config.yml", "settings.json", "appsettings.json",
		"docker-compose.yml", "docker-compose.yaml", "Dockerfile",
		".dockerignore", "kubernetes.yaml", "k8s.yaml",
	}

	for _, pattern := range envFiles {
		if matched, _ := filepath.Match(pattern, fileName); matched {
			return true
		}
		if strings.Contains(fileName, pattern) {
			return true
		}
	}

	return false
}

// isCICDFile checks if a file is a CI/CD configuration file
func (s *Scanner) isCICDFile(fileName string) bool {
	cicdFiles := []string{
		".github/workflows/*.yml", ".github/workflows/*.yaml",
		".gitlab-ci.yml", ".travis.yml", "circle.yml", "circleci/config.yml",
		"azure-pipelines.yml", "buildspec.yml", "cloudbuild.yaml",
		"Jenkinsfile", "bitbucket-pipelines.yml", "drone.yml",
		".buildkite/pipeline.yml", "wercker.yml", "shippable.yml",
	}

	for _, pattern := range cicdFiles {
		if matched, _ := filepath.Match(pattern, fileName); matched {
			return true
		}
		if strings.Contains(fileName, pattern) {
			return true
		}
	}

	return false
}

// shouldSkipDirectory checks if a directory should be completely skipped during scanning
func (s *Scanner) shouldSkipDirectory(dirName, fullPath string) bool {
	skipDirs := []string{
		"node_modules", "vendor", "target", "build", "dist",
		".git", ".svn", ".hg", ".bzr",
		"__pycache__", ".pytest_cache", ".coverage",
		".idea", ".vscode", ".vs",
		"bin", "obj", "packages", ".next", ".nuxt",
		"coverage", "tmp", "temp", "logs",
	}

	// Check directory name directly
	for _, skipDir := range skipDirs {
		if dirName == skipDir {
			return true
		}
	}

	// Check if any parent directory should be skipped
	for _, skipDir := range skipDirs {
		if strings.Contains(fullPath, string(filepath.Separator)+skipDir+string(filepath.Separator)) ||
			strings.HasSuffix(fullPath, string(filepath.Separator)+skipDir) {
			return true
		}
	}

	// Skip scanner source code directories to avoid false positives
	if s.isScannerSourcePath(fullPath) {
		return true
	}

	return false
}

// isScannerSourcePath checks if a path is part of the scanner source code
func (s *Scanner) isScannerSourcePath(path string) bool {
	scannerPaths := []string{
		"backend/pkg/bitcoin",
		"backend/pkg/ethereum",
		"backend/pkg/security",
		"backend/pkg/dependencies",
		"backend/pkg/scanner",
		"backend/pkg/models",
		"backend/pkg/api",
		"backend/pkg/database",
		"backend/cmd",
		"backend/internal",
	}

	for _, scannerPath := range scannerPaths {
		if strings.Contains(path, scannerPath) {
			return true
		}
	}

	return false
}

// shouldSkipPath checks if a path should be skipped during scanning
func (s *Scanner) shouldSkipPath(path string) bool {
	skipDirs := []string{
		"node_modules", "vendor", "target", "build", "dist",
		".git", ".svn", ".hg", ".bzr",
		"__pycache__", ".pytest_cache", ".coverage",
		".idea", ".vscode", ".vs",
		"bin", "obj", "packages",
	}

	for _, skipDir := range skipDirs {
		if strings.Contains(path, skipDir) {
			return true
		}
	}

	return false
}

// matchesPattern checks if a filename matches a supported file pattern
func (s *Scanner) matchesPattern(fileName, pattern string) bool {
	// Direct match
	if strings.Contains(fileName, pattern) {
		return true
	}

	// Glob pattern match
	if matched, err := filepath.Match(pattern, fileName); err == nil && matched {
		return true
	}

	// Case-insensitive match
	if strings.Contains(strings.ToLower(fileName), strings.ToLower(pattern)) {
		return true
	}

	return false
}

// GetDependencyInfo extracts dependency information from a project
func (s *Scanner) GetDependencyInfo(projectPath string) ([]DependencyInfo, error) {
	var dependencies []DependencyInfo

	// Walk through package files and extract dependency information
	err := filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip entire directories that should be excluded
		if info.IsDir() {
			if s.shouldSkipDirectory(info.Name(), path) {
				s.logger.Debugf("Skipping directory: %s", path)
				return filepath.SkipDir
			}
			return nil
		}

		// Skip hidden files
		if strings.HasPrefix(info.Name(), ".") {
			return nil
		}

		// Skip files in excluded paths
		if s.shouldSkipPath(path) {
			return nil
		}

		if s.isPackageFile(info.Name()) {
			deps, err := s.extractDependenciesFromFile(path)
			if err != nil {
				s.logger.Warnf("Failed to extract dependencies from %s: %v", path, err)
				return nil
			}
			dependencies = append(dependencies, deps...)
		}

		return nil
	})

	return dependencies, err
}

// extractDependenciesFromFile extracts dependency information from a specific file
func (s *Scanner) extractDependenciesFromFile(filePath string) ([]DependencyInfo, error) {
	// content, err := os.ReadFile(filePath)
	// if err != nil {
	// 	return nil, err
	// }

	fileName := filepath.Base(filePath)

	// Use appropriate analyzer based on file type
	for _, analyzer := range s.packageAnalyzers {
		for _, supportedFile := range analyzer.GetSupportedFiles() {
			if strings.Contains(fileName, supportedFile) {
				// This would need to be implemented in each analyzer
				// For now, return empty slice
				return []DependencyInfo{}, nil
			}
		}
	}

	return []DependencyInfo{}, nil
}

// CheckForVulnerabilities checks dependencies against vulnerability database
func (s *Scanner) CheckForVulnerabilities(dependencies []DependencyInfo) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	for _, dep := range dependencies {
		vulns, err := s.vulnerabilityDB.GetVulnerabilities(dep.Name, dep.Version, dep.PackageManager)
		if err != nil {
			s.logger.Warnf("Failed to check vulnerabilities for %s: %v", dep.Name, err)
			continue
		}

		for _, vuln := range vulns {
			issue := models.SecurityIssue{
				ID:          fmt.Sprintf("dep_vuln_%s_%s", dep.Name, vuln.ID),
				Type:        "dependency_vulnerability",
				Severity:    vuln.Severity,
				Title:       fmt.Sprintf("Vulnerable Dependency: %s", dep.Name),
				Description: fmt.Sprintf("%s (Version: %s) - %s", dep.Name, dep.Version, vuln.Description),
				File:        dep.FilePath,
				Line:        1,
				Code:        fmt.Sprintf("%s: %s", dep.Name, dep.Version),
				Chain:       "general",
				Category:    "dependency",
				CWE:         vuln.CWE,
				Suggestion:  fmt.Sprintf("Update to version %s or later", vuln.PatchedIn),
				References:  vuln.References,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			}
			issues = append(issues, issue)
		}
	}

	return issues, nil
}

// GenerateDependencyReport generates a comprehensive dependency report
func (s *Scanner) GenerateDependencyReport(projectPath string) (*DependencyReport, error) {
	dependencies, err := s.GetDependencyInfo(projectPath)
	if err != nil {
		return nil, err
	}

	vulnerabilityIssues, err := s.CheckForVulnerabilities(dependencies)
	if err != nil {
		return nil, err
	}

	report := &DependencyReport{
		ProjectPath:       projectPath,
		TotalDependencies: len(dependencies),
		Dependencies:      dependencies,
		Vulnerabilities:   vulnerabilityIssues,
		GeneratedAt:       time.Now(),
	}

	// Calculate statistics
	report.calculateStatistics()

	return report, nil
}

// DependencyReport represents a comprehensive dependency report
type DependencyReport struct {
	ProjectPath        string                 `json:"project_path"`
	TotalDependencies  int                    `json:"total_dependencies"`
	DirectDependencies int                    `json:"direct_dependencies"`
	DevDependencies    int                    `json:"dev_dependencies"`
	Dependencies       []DependencyInfo       `json:"dependencies"`
	Vulnerabilities    []models.SecurityIssue `json:"vulnerabilities"`
	CriticalVulns      int                    `json:"critical_vulnerabilities"`
	HighVulns          int                    `json:"high_vulnerabilities"`
	MediumVulns        int                    `json:"medium_vulnerabilities"`
	LowVulns           int                    `json:"low_vulnerabilities"`
	OutdatedPackages   int                    `json:"outdated_packages"`
	DeprecatedPackages int                    `json:"deprecated_packages"`
	PackageManagers    []string               `json:"package_managers"`
	GeneratedAt        time.Time              `json:"generated_at"`
}

// calculateStatistics calculates report statistics
func (r *DependencyReport) calculateStatistics() {
	packageManagers := make(map[string]bool)

	for _, dep := range r.Dependencies {
		packageManagers[dep.PackageManager] = true

		if dep.IsDirectDep {
			r.DirectDependencies++
		}
		if dep.IsDevDependency {
			r.DevDependencies++
		}
		if dep.Outdated {
			r.OutdatedPackages++
		}
		if dep.Deprecated {
			r.DeprecatedPackages++
		}
	}

	for _, vuln := range r.Vulnerabilities {
		switch vuln.Severity {
		case "critical":
			r.CriticalVulns++
		case "high":
			r.HighVulns++
		case "medium":
			r.MediumVulns++
		case "low":
			r.LowVulns++
		}
	}

	for pm := range packageManagers {
		r.PackageManagers = append(r.PackageManagers, pm)
	}
}
