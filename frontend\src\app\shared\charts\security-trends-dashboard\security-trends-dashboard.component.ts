import { Component, Input, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTabsModule } from '@angular/material/tabs';
import { Chart, ChartConfiguration, registerables } from 'chart.js';
import { Subject } from 'rxjs';

// Register Chart.js components
Chart.register(...registerables);

export interface TrendDataPoint {
  date: Date;
  critical: number;
  high: number;
  medium: number;
  low: number;
  totalScans: number;
  averageScore: number;
}

export interface SecurityTrend {
  period: string;
  data: TrendDataPoint[];
  summary: {
    totalIssues: number;
    criticalTrend: number;
    averageScore: number;
    scanFrequency: number;
  };
}

@Component({
  selector: 'app-security-trends-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatTabsModule
  ],
  templateUrl: './security-trends-dashboard.component.html',
  styleUrls: ['./security-trends-dashboard.component.scss']
})
export class SecurityTrendsDashboardComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('trendsCanvas', { static: true }) trendsCanvas!: ElementRef<HTMLCanvasElement>;
  @ViewChild('scoreCanvas', { static: true }) scoreCanvas!: ElementRef<HTMLCanvasElement>;
  @ViewChild('volumeCanvas', { static: true }) volumeCanvas!: ElementRef<HTMLCanvasElement>;

  @Input() trendsData: SecurityTrend | null = null;
  @Input() showComparison: boolean = true;
  @Input() autoRefresh: boolean = false;

  private destroy$ = new Subject<void>();
  private trendsChart: Chart | null = null;
  private scoreChart: Chart | null = null;
  private volumeChart: Chart | null = null;

  selectedPeriod: string = '30d';
  selectedView: string = 'trends';
  selectedTabIndex: number = 0;
  isLoading: boolean = false;

  // Make Math available in template
  Math = Math;

  periods = [
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 90 Days' },
    { value: '1y', label: 'Last Year' }
  ];

  chartViews = [
    { value: 'trends', label: 'Security Trends', icon: 'trending_up' },
    { value: 'scores', label: 'Security Scores', icon: 'grade' },
    { value: 'volume', label: 'Scan Volume', icon: 'bar_chart' }
  ];

  ngOnInit(): void {
    // Component initialization
  }

  ngAfterViewInit(): void {
    this.createCharts();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.destroyCharts();
  }

  private destroyCharts(): void {
    if (this.trendsChart) {
      this.trendsChart.destroy();
      this.trendsChart = null;
    }
    if (this.scoreChart) {
      this.scoreChart.destroy();
      this.scoreChart = null;
    }
    if (this.volumeChart) {
      this.volumeChart.destroy();
      this.volumeChart = null;
    }
  }

  private createCharts(): void {
    this.createTrendsChart();
    this.createScoreChart();
    this.createVolumeChart();
  }

  private createTrendsChart(): void {
    if (!this.trendsCanvas?.nativeElement || !this.trendsData) {
      return;
    }

    const ctx = this.trendsCanvas.nativeElement.getContext('2d');
    if (!ctx) return;

    if (this.trendsChart) {
      this.trendsChart.destroy();
    }

    const labels = this.trendsData.data.map(point => 
      point.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    );

    const config: ChartConfiguration = {
      type: 'line',
      data: {
        labels,
        datasets: [
          {
            label: 'Critical',
            data: this.trendsData.data.map(point => point.critical),
            borderColor: '#dc2626',
            backgroundColor: 'rgba(220, 38, 38, 0.1)',
            borderWidth: 3,
            fill: false,
            tension: 0.4
          },
          {
            label: 'High',
            data: this.trendsData.data.map(point => point.high),
            borderColor: '#ea580c',
            backgroundColor: 'rgba(234, 88, 12, 0.1)',
            borderWidth: 2,
            fill: false,
            tension: 0.4
          },
          {
            label: 'Medium',
            data: this.trendsData.data.map(point => point.medium),
            borderColor: '#d97706',
            backgroundColor: 'rgba(217, 119, 6, 0.1)',
            borderWidth: 2,
            fill: false,
            tension: 0.4
          },
          {
            label: 'Low',
            data: this.trendsData.data.map(point => point.low),
            borderColor: '#65a30d',
            backgroundColor: 'rgba(101, 163, 13, 0.1)',
            borderWidth: 2,
            fill: false,
            tension: 0.4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          mode: 'index',
          intersect: false,
        },
        plugins: {
          legend: {
            display: true,
            position: 'top',
            labels: {
              usePointStyle: true,
              padding: 20,
              font: {
                family: 'Inter, sans-serif',
                size: 12,
                weight: 500
              }
            }
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: '#374151',
            borderWidth: 1,
            cornerRadius: 8
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Date',
              font: {
                family: 'Inter, sans-serif',
                weight: 500
              }
            },
            grid: {
              color: '#f3f4f6'
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Number of Issues',
              font: {
                family: 'Inter, sans-serif',
                weight: 500
              }
            },
            beginAtZero: true,
            grid: {
              color: '#f3f4f6'
            }
          }
        },
        animation: {
          duration: 1000,
          easing: 'easeInOutQuart'
        }
      }
    };

    this.trendsChart = new Chart(ctx, config);
  }

  private createScoreChart(): void {
    if (!this.scoreCanvas?.nativeElement || !this.trendsData) {
      return;
    }

    const ctx = this.scoreCanvas.nativeElement.getContext('2d');
    if (!ctx) return;

    if (this.scoreChart) {
      this.scoreChart.destroy();
    }

    const labels = this.trendsData.data.map(point => 
      point.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    );

    const config: ChartConfiguration = {
      type: 'bar',
      data: {
        labels,
        datasets: [
          {
            label: 'Security Score',
            data: this.trendsData.data.map(point => point.averageScore),
            backgroundColor: this.trendsData.data.map(point => 
              point.averageScore >= 80 ? '#10b981' :
              point.averageScore >= 60 ? '#f59e0b' :
              point.averageScore >= 40 ? '#ef4444' : '#dc2626'
            ),
            borderColor: this.trendsData.data.map(point => 
              point.averageScore >= 80 ? '#059669' :
              point.averageScore >= 60 ? '#d97706' :
              point.averageScore >= 40 ? '#dc2626' : '#b91c1c'
            ),
            borderWidth: 1,
            borderRadius: 4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: '#374151',
            borderWidth: 1,
            cornerRadius: 8,
            callbacks: {
              label: (context) => {
                return `Security Score: ${context.parsed.y}/100`;
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Date',
              font: {
                family: 'Inter, sans-serif',
                weight: 500
              }
            },
            grid: {
              display: false
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Security Score',
              font: {
                family: 'Inter, sans-serif',
                weight: 500
              }
            },
            min: 0,
            max: 100,
            grid: {
              color: '#f3f4f6'
            }
          }
        },
        animation: {
          duration: 1000,
          easing: 'easeInOutQuart'
        }
      }
    };

    this.scoreChart = new Chart(ctx, config);
  }

  private createVolumeChart(): void {
    if (!this.volumeCanvas?.nativeElement || !this.trendsData) {
      return;
    }

    const ctx = this.volumeCanvas.nativeElement.getContext('2d');
    if (!ctx) return;

    if (this.volumeChart) {
      this.volumeChart.destroy();
    }

    const labels = this.trendsData.data.map(point => 
      point.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    );

    const config: ChartConfiguration = {
      type: 'bar',
      data: {
        labels,
        datasets: [
          {
            label: 'Scans Performed',
            data: this.trendsData.data.map(point => point.totalScans),
            backgroundColor: '#3b82f6',
            borderColor: '#2563eb',
            borderWidth: 1,
            borderRadius: 4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: '#374151',
            borderWidth: 1,
            cornerRadius: 8
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Date',
              font: {
                family: 'Inter, sans-serif',
                weight: 500
              }
            },
            grid: {
              display: false
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Number of Scans',
              font: {
                family: 'Inter, sans-serif',
                weight: 500
              }
            },
            beginAtZero: true,
            grid: {
              color: '#f3f4f6'
            }
          }
        },
        animation: {
          duration: 1000,
          easing: 'easeInOutQuart'
        }
      }
    };

    this.volumeChart = new Chart(ctx, config);
  }

  onPeriodChange(period: string): void {
    this.selectedPeriod = period;
    this.refreshData();
  }

  onViewChange(view: string): void {
    this.selectedView = view;
  }

  refreshData(): void {
    this.isLoading = true;
    // Simulate data refresh
    setTimeout(() => {
      this.createCharts();
      this.isLoading = false;
    }, 500);
  }

  exportChart(): void {
    let chart: Chart | null = null;
    let filename = '';

    switch (this.selectedView) {
      case 'trends':
        chart = this.trendsChart;
        filename = 'security-trends';
        break;
      case 'scores':
        chart = this.scoreChart;
        filename = 'security-scores';
        break;
      case 'volume':
        chart = this.volumeChart;
        filename = 'scan-volume';
        break;
    }

    if (chart) {
      const url = chart.toBase64Image();
      const link = document.createElement('a');
      link.download = `${filename}-${this.selectedPeriod}.png`;
      link.href = url;
      link.click();
    }
  }

  getTrendDirection(value: number): 'up' | 'down' | 'flat' {
    if (value > 5) return 'up';
    if (value < -5) return 'down';
    return 'flat';
  }

  getTrendIcon(direction: 'up' | 'down' | 'flat'): string {
    switch (direction) {
      case 'up': return 'trending_up';
      case 'down': return 'trending_down';
      case 'flat': return 'trending_flat';
    }
  }

  getTrendColor(direction: 'up' | 'down' | 'flat', isGoodTrend: boolean = false): string {
    if (direction === 'flat') return 'var(--spt-text-secondary)';
    
    if (isGoodTrend) {
      return direction === 'up' ? 'var(--spt-success-600)' : 'var(--spt-error-600)';
    } else {
      return direction === 'up' ? 'var(--spt-error-600)' : 'var(--spt-success-600)';
    }
  }
}
