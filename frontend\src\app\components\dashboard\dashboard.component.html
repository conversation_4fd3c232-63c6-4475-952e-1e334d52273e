<div class="dashboard-container">
  <!-- Enhanced Header Section -->
  <div class="dashboard-header">
    <div class="header-content">
      <div class="header-text">
        <h1 class="dashboard-title">
          <mat-icon class="title-icon">dashboard</mat-icon>
          Security Dashboard
        </h1>
        <p class="dashboard-subtitle">Monitor your blockchain security posture in real-time</p>
      </div>
      <div class="header-stats">
        <div class="quick-stat">
          <span class="stat-value">{{ totalScans }}</span>
          <span class="stat-label">Total Scans</span>
        </div>
        <div class="quick-stat">
          <span class="stat-value">{{ activeScans }}</span>
          <span class="stat-label">Active</span>
        </div>
        <div class="quick-stat">
          <span class="stat-value">{{ criticalIssues }}</span>
          <span class="stat-label">Critical</span>
        </div>
      </div>
    </div>
    <div class="header-actions">
      <button mat-raised-button color="primary" routerLink="/scan" class="action-button primary">
        <mat-icon>security</mat-icon>
        Start New Scan
      </button>
      <button mat-stroked-button routerLink="/reports" class="action-button secondary">
        <mat-icon>assessment</mat-icon>
        View Reports
      </button>
    </div>
  </div>

  <!-- Enhanced Stats Grid -->
  <div class="stats-grid">
    <mat-card class="stat-card total-scans elevated">
      <div class="stat-card-header">
        <div class="stat-icon-wrapper success">
          <mat-icon class="stat-icon">security</mat-icon>
        </div>
        <div class="stat-trend">
          <mat-icon class="trend-icon positive">trending_up</mat-icon>
          <span class="trend-value">+12%</span>
        </div>
      </div>
      <div class="stat-card-content">
        <div class="stat-number">{{ stats.totalScans }}</div>
        <div class="stat-label">Total Scans</div>
        <div class="stat-description">Completed this month</div>
      </div>
      <div class="stat-card-footer">
        <button mat-button color="primary" routerLink="/scan">
          <mat-icon>add</mat-icon>
          New Scan
        </button>
      </div>
    </mat-card>

    <mat-card class="stat-card critical-issues elevated">
      <div class="stat-card-header">
        <div class="stat-icon-wrapper critical">
          <mat-icon class="stat-icon">error</mat-icon>
        </div>
        <div class="stat-trend">
          <mat-icon class="trend-icon negative">trending_up</mat-icon>
          <span class="trend-value">+3</span>
        </div>
      </div>
      <div class="stat-card-content">
        <div class="stat-number critical">{{ stats.criticalIssues }}</div>
        <div class="stat-label">Critical Issues</div>
        <div class="stat-description">Require immediate attention</div>
      </div>
      <div class="stat-card-footer">
        <button mat-button color="warn" routerLink="/reports">
          <mat-icon>priority_high</mat-icon>
          View Details
        </button>
      </div>
    </mat-card>

    <mat-card class="stat-card high-issues elevated">
      <div class="stat-card-header">
        <div class="stat-icon-wrapper warning">
          <mat-icon class="stat-icon">warning</mat-icon>
        </div>
        <div class="stat-trend">
          <mat-icon class="trend-icon neutral">trending_flat</mat-icon>
          <span class="trend-value">0</span>
        </div>
      </div>
      <div class="stat-card-content">
        <div class="stat-number warning">{{ stats.highIssues }}</div>
        <div class="stat-label">High Priority</div>
        <div class="stat-description">Should be addressed soon</div>
      </div>
      <div class="stat-card-footer">
        <button mat-button color="accent" routerLink="/reports">
          <mat-icon>visibility</mat-icon>
          Review
        </button>
      </div>
    </mat-card>

    <mat-card class="stat-card medium-issues elevated">
      <div class="stat-card-header">
        <div class="stat-icon-wrapper info">
          <mat-icon class="stat-icon">info</mat-icon>
        </div>
        <div class="stat-trend">
          <mat-icon class="trend-icon positive">trending_down</mat-icon>
          <span class="trend-value">-5</span>
        </div>
      </div>
      <div class="stat-card-content">
        <div class="stat-number info">{{ stats.mediumIssues }}</div>
        <div class="stat-label">Medium Priority</div>
        <div class="stat-description">Monitor and plan fixes</div>
      </div>
      <div class="stat-card-footer">
        <button mat-button routerLink="/checklist">
          <mat-icon>checklist</mat-icon>
          Checklist
        </button>
      </div>
    </mat-card>
  </div>

  <!-- Security Metrics Chart -->
  <div class="chart-section">
    <app-security-metrics-chart
      title="Security Issues Distribution"
      [data]="getSecurityMetricsData()"
      [chartType]="'doughnut'"
      [showTrends]="true"
      [animated]="true">
    </app-security-metrics-chart>
  </div>

  <!-- Active Scan Progress -->
  <div class="chart-section" *ngIf="getActiveScan()">
    <app-scan-progress-chart
      [scanProgress]="getActiveScan()"
      [showRealTimeUpdates]="true"
      [updateInterval]="2000">
    </app-scan-progress-chart>
  </div>

  <!-- Security Trends Dashboard -->
  <div class="chart-section">
    <app-security-trends-dashboard
      [trendsData]="getSecurityTrendsData()"
      [showComparison]="true"
      [autoRefresh]="false">
    </app-security-trends-dashboard>
  </div>

  <!-- Recent Scans Table -->
  <mat-card class="recent-scans-card">
    <mat-card-header>
      <mat-card-title>Recent Scans</mat-card-title>
      <mat-card-subtitle>Latest security scan results</mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <div class="table-container">
        <table mat-table [dataSource]="recentScans" class="scans-table">
          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef>ID</th>
            <td mat-cell *matCellDef="let scan">
              <span class="scan-id">{{ scan.id.substring(0, 8) }}...</span>
            </td>
          </ng-container>

          <!-- Project Column -->
          <ng-container matColumnDef="project">
            <th mat-header-cell *matHeaderCellDef>Project</th>
            <td mat-cell *matCellDef="let scan">
              <div class="project-info">
                <span class="project-name">{{ getProjectName(scan.project_path) }}</span>
                <span class="project-path">{{ scan.project_path }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Chains Column -->
          <ng-container matColumnDef="chains">
            <th mat-header-cell *matHeaderCellDef>Chains</th>
            <td mat-cell *matCellDef="let scan">
              <div class="chains-list">
                <mat-chip-set>
                  <mat-chip *ngFor="let chain of scan.chains">{{ chain }}</mat-chip>
                </mat-chip-set>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let scan">
              <div class="status-badge" [class]="'status-' + scan.status">
                <mat-icon>{{ getStatusIcon(scan.status) }}</mat-icon>
                <span>{{ scan.status | titlecase }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Issues Column -->
          <ng-container matColumnDef="issues">
            <th mat-header-cell *matHeaderCellDef>Issues</th>
            <td mat-cell *matCellDef="let scan">
              <div class="issues-summary">
                <span class="issue-count critical" *ngIf="getIssueCount(scan, 'critical') > 0">
                  {{ getIssueCount(scan, 'critical') }} Critical
                </span>
                <span class="issue-count high" *ngIf="getIssueCount(scan, 'high') > 0">
                  {{ getIssueCount(scan, 'high') }} High
                </span>
                <span class="issue-count medium" *ngIf="getIssueCount(scan, 'medium') > 0">
                  {{ getIssueCount(scan, 'medium') }} Medium
                </span>
                <span class="no-issues" *ngIf="!scan.issues || scan.issues.length === 0">
                  No issues found
                </span>
              </div>
            </td>
          </ng-container>

          <!-- Date Column -->
          <ng-container matColumnDef="date">
            <th mat-header-cell *matHeaderCellDef>Date</th>
            <td mat-cell *matCellDef="let scan">
              <div class="date-info">
                <span class="date">{{ scan.created_at | date:'MMM d, y' }}</span>
                <span class="time">{{ scan.created_at | date:'h:mm a' }}</span>
              </div>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let scan">
              <div class="action-buttons">
                <button mat-icon-button 
                        [routerLink]="['/reports', scan.id]"
                        matTooltip="View Report">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button mat-icon-button 
                        *ngIf="scan.status === 'completed'"
                        matTooltip="Download Report">
                  <mat-icon>download</mat-icon>
                </button>
                <button mat-icon-button 
                        *ngIf="scan.status === 'running'"
                        matTooltip="View Progress">
                  <mat-icon>hourglass_empty</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Quick Actions -->
  <mat-card class="quick-actions-card">
    <mat-card-header>
      <mat-card-title>Quick Actions</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="actions-grid">
        <button mat-raised-button color="primary" routerLink="/scan">
          <mat-icon>security</mat-icon>
          Start New Scan
        </button>
        <button mat-raised-button color="accent" routerLink="/checklist">
          <mat-icon>checklist</mat-icon>
          Security Checklist
        </button>
        <button mat-raised-button routerLink="/reports">
          <mat-icon>assessment</mat-icon>
          Generate Report
        </button>
        <button mat-raised-button routerLink="/settings">
          <mat-icon>settings</mat-icon>
          Settings
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</div>
