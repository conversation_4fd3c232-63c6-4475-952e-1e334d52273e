# VSCode Extension Deployment and Publishing Guide

This guide covers how to deploy and publish the Blockchain Security Protocol (SPT) VSCode extension to the Visual Studio Code Marketplace and other distribution channels.

## Prerequisites

Before deploying the extension, ensure you have:

- **Node.js 18+** installed
- **npm** or **yarn** package manager
- **Visual Studio Code Extension Manager (vsce)** installed globally
- **Azure DevOps account** (for marketplace publishing)
- **Publisher account** on the VS Code Marketplace

## Setup and Installation

### 1. Install Required Tools

```bash
# Install Visual Studio Code Extension Manager globally
npm install -g @vscode/vsce

# Verify installation
vsce --version
```

### 2. Install Project Dependencies

```bash
# Navigate to the extension directory
cd vscode-extension

# Install dependencies
npm install

# Install development dependencies
npm install --save-dev
```

## Building the Extension

### 1. Compile TypeScript

```bash
# Compile TypeScript to JavaScript
npm run compile

# Or watch for changes during development
npm run watch
```

### 2. Run Tests and Linting

```bash
# Run linting
npm run lint

# Run basic tests (currently simplified for CI/CD compatibility)
npm test

# Run all pre-publish checks
npm run pretest

# For comprehensive testing, see TESTING.md in the extension directory
```

### 3. Package the Extension

```bash
# Create a .vsix package file
npm run package

# Or use vsce directly
vsce package
```

This creates a `.vsix` file (e.g., `blockchain-security-protocol-1.0.0.vsix`) that can be installed locally or distributed.

## Local Testing and Installation

### 1. Install from VSIX File

```bash
# Install the packaged extension locally
code --install-extension blockchain-security-protocol-1.0.0.vsix

# Or install from VSCode UI:
# 1. Open VSCode
# 2. Go to Extensions (Ctrl+Shift+X)
# 3. Click "..." menu → "Install from VSIX..."
# 4. Select the .vsix file
```

### 2. Test the Extension

1. **Open a blockchain project** with Solidity files
2. **Verify activation** - Check that SPT commands appear in Command Palette
3. **Test core features**:
   - File scanning
   - Project scanning
   - Security issue detection
   - Configuration settings
4. **Check integration** with SPT backend server

## Publishing to VS Code Marketplace

### 1. Create Publisher Account

1. **Sign up** at [Visual Studio Marketplace](https://marketplace.visualstudio.com/manage)
2. **Create a publisher** with ID matching `package.json` publisher field
3. **Generate Personal Access Token** in Azure DevOps

### 2. Login to Marketplace

```bash
# Login with your publisher account
vsce login <publisher-name>

# Enter your Personal Access Token when prompted
```

### 3. Publish the Extension

```bash
# Publish to marketplace (auto-increments version)
npm run publish

# Or use vsce directly
vsce publish

# Publish specific version
vsce publish 1.0.1

# Publish with custom message
vsce publish -m "Added new security rules for DeFi protocols"
```

### 4. Verify Publication

1. **Check marketplace** - Visit your extension page
2. **Test installation** - Install from marketplace
3. **Monitor metrics** - Check download and rating statistics

## Version Management

### 1. Semantic Versioning

Follow semantic versioning (semver) for releases:

- **Major** (1.0.0 → 2.0.0): Breaking changes
- **Minor** (1.0.0 → 1.1.0): New features, backward compatible
- **Patch** (1.0.0 → 1.0.1): Bug fixes, backward compatible

### 2. Update Version

```bash
# Update version in package.json
npm version patch   # 1.0.0 → 1.0.1
npm version minor   # 1.0.0 → 1.1.0
npm version major   # 1.0.0 → 2.0.0

# Or manually edit package.json version field
```

### 3. Pre-release Versions

```bash
# Publish pre-release version
vsce publish --pre-release

# Publish specific pre-release version
vsce publish 1.1.0-beta.1 --pre-release
```

## Alternative Distribution Methods

### 1. GitHub Releases

```bash
# Create release package
npm run package

# Upload .vsix file to GitHub Releases
# Users can download and install manually
```

### 2. Private Distribution

```bash
# Package for private distribution
vsce package

# Distribute .vsix file through:
# - Internal company repositories
# - Email or file sharing
# - Private package registries
```

### 3. Open VSX Registry

```bash
# Install ovsx CLI
npm install -g ovsx

# Login to Open VSX
ovsx create-namespace <namespace>

# Publish to Open VSX (for non-Microsoft editors)
ovsx publish blockchain-security-protocol-1.0.0.vsix
```

## Continuous Integration/Deployment

### 1. GitHub Actions Workflow

Create `.github/workflows/publish-extension.yml`:

```yaml
name: Publish VSCode Extension

on:
  release:
    types: [published]

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: |
          cd vscode-extension
          npm ci
          
      - name: Run tests
        run: |
          cd vscode-extension
          npm test
          
      - name: Package extension
        run: |
          cd vscode-extension
          npm run package
          
      - name: Publish to marketplace
        run: |
          cd vscode-extension
          npx vsce publish
        env:
          VSCE_PAT: ${{ secrets.VSCE_PAT }}
```

### 2. Environment Variables

Set up required secrets in your repository:

- `VSCE_PAT`: Personal Access Token for VS Code Marketplace
- `OVSX_PAT`: Personal Access Token for Open VSX Registry

## Post-Publication Tasks

### 1. Update Documentation

- **Update README.md** with new features
- **Update CHANGELOG.md** with release notes
- **Update version references** in documentation

### 2. Monitor and Support

- **Monitor marketplace reviews** and ratings
- **Respond to user issues** on GitHub
- **Track download metrics** and usage analytics
- **Plan future releases** based on feedback

### 3. Marketing and Promotion

- **Announce release** on social media
- **Update project website** with new version info
- **Write blog posts** about new features
- **Engage with blockchain development community**

## Troubleshooting

### Common Issues

**Authentication Errors**
```bash
# Re-login to marketplace
vsce logout
vsce login <publisher-name>
```

**Package Size Too Large**
```bash
# Check package contents
vsce ls

# Add files to .vscodeignore to reduce size
echo "node_modules/" >> .vscodeignore
echo "src/" >> .vscodeignore
echo "*.ts" >> .vscodeignore
```

**Version Conflicts**
```bash
# Check current published version
vsce show <publisher>.<extension-name>

# Increment version appropriately
npm version patch
```

**Build Failures**
```bash
# Clean and rebuild
rm -rf out/
rm -rf node_modules/
npm install
npm run compile
```

## Security Considerations

### 1. Secure Token Management

- **Never commit** Personal Access Tokens to version control
- **Use environment variables** or secure secret management
- **Rotate tokens regularly** for security

### 2. Code Security

- **Audit dependencies** for vulnerabilities
- **Use HTTPS** for all external communications
- **Validate user inputs** in extension code
- **Follow VSCode security guidelines**

### 3. Privacy

- **Minimize data collection** in the extension
- **Clearly document** what data is sent to SPT backend
- **Provide opt-out options** for telemetry and analytics

## Best Practices

1. **Test thoroughly** before publishing
2. **Use semantic versioning** consistently
3. **Maintain backward compatibility** when possible
4. **Document breaking changes** clearly
5. **Respond promptly** to user feedback
6. **Keep dependencies updated** and secure
7. **Follow VSCode extension guidelines** and best practices

---

For more information, see:
- [VSCode Extension Publishing Guide](https://code.visualstudio.com/api/working-with-extensions/publishing-extension)
- [Visual Studio Marketplace](https://marketplace.visualstudio.com/)
- [Extension Development Best Practices](https://code.visualstudio.com/api/references/extension-guidelines)
