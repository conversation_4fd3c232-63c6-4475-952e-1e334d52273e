<div class="progress-container" *ngIf="scanProgress">
  <!-- Progress Header -->
  <div class="progress-header">
    <div class="scan-info">
      <div class="scan-title">
        <mat-icon [style.color]="getStatusColor()" class="status-icon">
          {{ getStatusIcon() }}
        </mat-icon>
        <h3>{{ scanProgress.projectName }}</h3>
        <span class="scan-id">ID: {{ scanProgress.scanId }}</span>
      </div>
      
      <div class="scan-status" [class]="'status-' + scanProgress.status">
        <span class="status-text">{{ scanProgress.status | titlecase }}</span>
        <span class="current-step">{{ scanProgress.currentStep }}</span>
      </div>
    </div>

    <div class="progress-actions" *ngIf="scanProgress.status === 'running'">
      <button 
        mat-icon-button 
        matTooltip="Pause Scan"
        (click)="pauseScan()"
        class="action-btn pause">
        <mat-icon>pause</mat-icon>
      </button>
      
      <button 
        mat-icon-button 
        matTooltip="Cancel Scan"
        (click)="cancelScan()"
        class="action-btn cancel">
        <mat-icon>stop</mat-icon>
      </button>
    </div>
  </div>

  <!-- Main Progress Bar -->
  <div class="main-progress">
    <div class="progress-info">
      <span class="progress-label">Overall Progress</span>
      <span class="progress-percentage">{{ animatedProgress | number:'1.1-1' }}%</span>
    </div>
    
    <mat-progress-bar 
      mode="determinate" 
      [value]="animatedProgress"
      class="main-progress-bar">
    </mat-progress-bar>
    
    <div class="progress-details">
      <span>Step {{ scanProgress.completedSteps }} of {{ scanProgress.totalSteps }}</span>
      <span>{{ animatedFilesScanned | number:'1.0-0' }} / {{ scanProgress.totalFiles }} files</span>
    </div>
  </div>

  <!-- Progress Stats Grid -->
  <div class="progress-stats">
    <div class="stat-card files">
      <div class="stat-icon">
        <mat-icon>description</mat-icon>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ animatedFilesScanned | number:'1.0-0' }}</div>
        <div class="stat-label">Files Scanned</div>
        <div class="stat-detail">of {{ scanProgress.totalFiles }} total</div>
      </div>
    </div>

    <div class="stat-card issues">
      <div class="stat-icon">
        <mat-icon>bug_report</mat-icon>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ animatedIssuesFound | number:'1.0-0' }}</div>
        <div class="stat-label">Issues Found</div>
        <div class="stat-detail">{{ (animatedIssuesFound / Math.max(animatedFilesScanned, 1) * 100) | number:'1.1-1' }}% of files</div>
      </div>
    </div>

    <div class="stat-card time">
      <div class="stat-icon">
        <mat-icon>schedule</mat-icon>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ getElapsedTime() }}</div>
        <div class="stat-label">Elapsed Time</div>
        <div class="stat-detail" *ngIf="scanProgress.status === 'running'">
          {{ getEstimatedTimeRemaining() }} remaining
        </div>
      </div>
    </div>

    <div class="stat-card speed">
      <div class="stat-icon">
        <mat-icon>speed</mat-icon>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ (animatedFilesScanned / Math.max(getElapsedTimeInMinutes(), 1)) | number:'1.1-1' }}</div>
        <div class="stat-label">Files/Min</div>
        <div class="stat-detail">Scan Speed</div>
      </div>
    </div>
  </div>

  <!-- Real-time Progress Chart -->
  <div class="progress-chart" *ngIf="showRealTimeUpdates && progressHistory.length > 1">
    <div class="chart-header">
      <h4>Real-time Progress</h4>
      <div class="chart-legend">
        <div class="legend-item">
          <div class="legend-color progress"></div>
          <span>Progress</span>
        </div>
        <div class="legend-item">
          <div class="legend-color issues"></div>
          <span>Issues Found</span>
        </div>
      </div>
    </div>
    
    <div class="chart-content">
      <canvas 
        #progressCanvas
        class="progress-canvas"
        [attr.aria-label]="'Real-time progress chart for ' + scanProgress.projectName">
      </canvas>
    </div>
  </div>

  <!-- Step Progress -->
  <div class="step-progress" *ngIf="scanProgress.status === 'running'">
    <h4>Scan Steps</h4>
    <div class="steps-list">
      <div 
        *ngFor="let step of getScanSteps(); let i = index"
        class="step-item"
        [class.completed]="i < scanProgress.completedSteps"
        [class.current]="i === scanProgress.completedSteps"
        [class.pending]="i > scanProgress.completedSteps">
        
        <div class="step-indicator">
          <mat-icon *ngIf="i < scanProgress.completedSteps">check</mat-icon>
          <mat-icon *ngIf="i === scanProgress.completedSteps" class="spinning">hourglass_empty</mat-icon>
          <span *ngIf="i > scanProgress.completedSteps">{{ i + 1 }}</span>
        </div>
        
        <div class="step-content">
          <div class="step-name">{{ step.name }}</div>
          <div class="step-description">{{ step.description }}</div>
        </div>
        
        <div class="step-status">
          <mat-icon *ngIf="i < scanProgress.completedSteps" class="completed-icon">check_circle</mat-icon>
          <mat-progress-bar 
            *ngIf="i === scanProgress.completedSteps"
            mode="indeterminate"
            class="step-progress-bar">
          </mat-progress-bar>
        </div>
      </div>
    </div>
  </div>

  <!-- Completion Summary -->
  <div class="completion-summary" *ngIf="scanProgress.status === 'completed' || scanProgress.status === 'failed'">
    <div class="summary-header">
      <mat-icon [style.color]="getStatusColor()" class="summary-icon">
        {{ getStatusIcon() }}
      </mat-icon>
      <h4>Scan {{ scanProgress.status | titlecase }}</h4>
    </div>
    
    <div class="summary-stats">
      <div class="summary-item">
        <span class="label">Total Files Scanned:</span>
        <span class="value">{{ scanProgress.filesScanned }}</span>
      </div>
      <div class="summary-item">
        <span class="label">Issues Found:</span>
        <span class="value">{{ scanProgress.issuesFound }}</span>
      </div>
      <div class="summary-item">
        <span class="label">Total Time:</span>
        <span class="value">{{ getElapsedTime() }}</span>
      </div>
    </div>
    
    <div class="summary-actions">
      <button mat-raised-button color="primary" *ngIf="scanProgress.status === 'completed'">
        <mat-icon>visibility</mat-icon>
        View Report
      </button>
      <button mat-stroked-button *ngIf="scanProgress.status === 'failed'">
        <mat-icon>refresh</mat-icon>
        Retry Scan
      </button>
    </div>
  </div>
</div>

<!-- Empty State -->
<div class="empty-state" *ngIf="!scanProgress">
  <mat-icon class="empty-icon">hourglass_empty</mat-icon>
  <h4>No Active Scan</h4>
  <p>Start a new security scan to see real-time progress here.</p>
  <button mat-raised-button color="primary">
    <mat-icon>security</mat-icon>
    Start New Scan
  </button>
</div>
