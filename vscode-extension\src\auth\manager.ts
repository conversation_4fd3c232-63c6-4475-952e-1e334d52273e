import * as vscode from 'vscode';
import * as crypto from 'crypto';
import { ConfigurationManager } from '../config/manager';

export interface AuthState {
    isAuthenticated: boolean;
    token?: string;
    user?: {
        id: string;
        username: string;
        email: string;
        role: string;
    };
    expiresAt?: number;
}

export class AuthenticationManager {
    private static readonly AUTH_STATE_KEY = 'spt.authState';
    private static readonly PENDING_AUTH_KEY = 'spt.pendingAuth';
    
    private authState: AuthState = { isAuthenticated: false };
    private authChangeEmitter = new vscode.EventEmitter<AuthState>();
    public readonly onAuthStateChanged = this.authChangeEmitter.event;

    constructor(
        private context: vscode.ExtensionContext,
        private configManager: ConfigurationManager
    ) {
        this.loadAuthState();
    }

    /**
     * Check if user is currently authenticated
     */
    isAuthenticated(): boolean {
        // Check if token exists and is not expired
        if (!this.authState.token || !this.authState.expiresAt) {
            return false;
        }

        // Check if token is expired
        if (Date.now() >= this.authState.expiresAt) {
            this.clearAuthState();
            return false;
        }

        return this.authState.isAuthenticated;
    }

    /**
     * Get current authentication state
     */
    getAuthState(): AuthState {
        return { ...this.authState };
    }

    /**
     * Get current JWT token
     */
    getToken(): string | undefined {
        if (this.isAuthenticated()) {
            return this.authState.token;
        }
        return undefined;
    }

    /**
     * Initiate authentication flow - redirect to web portal
     */
    async authenticate(): Promise<boolean> {
        try {
            // Generate a unique state parameter for security
            const state = crypto.randomBytes(32).toString('hex');
            const webPortalUrl = this.configManager.getWebPortalUrl();

            // Store pending auth state
            await this.context.globalState.update(AuthenticationManager.PENDING_AUTH_KEY, {
                state,
                timestamp: Date.now()
            });

            // Construct authentication URL - redirect directly to frontend login page
            const authUrl = `${webPortalUrl}/auth/login?vscode=true&state=${state}&redirect_uri=vscode://blockchain-security-protocol.spt/auth/callback`;

            // Show information message to user
            const result = await vscode.window.showInformationMessage(
                'Authentication required. You will be redirected to the SPT web portal to sign in.',
                'Open Browser',
                'Cancel'
            );

            if (result === 'Open Browser') {
                // Open the authentication URL in the default browser
                await vscode.env.openExternal(vscode.Uri.parse(authUrl));
                
                // Show progress while waiting for authentication
                return await vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: 'Waiting for authentication...',
                    cancellable: true
                }, async (progress, token) => {
                    return new Promise<boolean>((resolve) => {
                        // Set up timeout (5 minutes)
                        const timeout = setTimeout(() => {
                            resolve(false);
                        }, 5 * 60 * 1000);

                        // Listen for authentication completion
                        const disposable = this.onAuthStateChanged((authState) => {
                            if (authState.isAuthenticated) {
                                clearTimeout(timeout);
                                disposable.dispose();
                                resolve(true);
                            }
                        });

                        // Handle cancellation
                        token.onCancellationRequested(() => {
                            clearTimeout(timeout);
                            disposable.dispose();
                            resolve(false);
                        });
                    });
                });
            }

            return false;
        } catch (error) {
            vscode.window.showErrorMessage(`Authentication failed: ${error}`);
            return false;
        }
    }

    /**
     * Handle authentication callback from web portal
     */
    async handleAuthCallback(token: string, user: any, expiresIn: number): Promise<void> {
        try {
            console.log('AuthManager: Handling auth callback', { token: token ? 'present' : 'missing', user, expiresIn });

            // Calculate expiration time
            const expiresAt = Date.now() + (expiresIn * 1000);

            // Update auth state
            this.authState = {
                isAuthenticated: true,
                token,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: user.role
                },
                expiresAt
            };

            console.log('AuthManager: Updated auth state', this.authState);

            // Save to persistent storage
            await this.saveAuthState();
            console.log('AuthManager: Saved auth state to storage');

            // Clear pending auth
            await this.context.globalState.update(AuthenticationManager.PENDING_AUTH_KEY, undefined);
            console.log('AuthManager: Cleared pending auth');

            // Notify listeners
            this.authChangeEmitter.fire(this.authState);
            console.log('AuthManager: Notified listeners');

            vscode.window.showInformationMessage(`Successfully authenticated as ${user.username}`);
        } catch (error) {
            console.error('AuthManager: Failed to complete authentication:', error);
            vscode.window.showErrorMessage(`Failed to complete authentication: ${error}`);
        }
    }

    /**
     * Sign out the user
     */
    async signOut(): Promise<void> {
        this.clearAuthState();
        await this.saveAuthState();
        this.authChangeEmitter.fire(this.authState);
        vscode.window.showInformationMessage('Successfully signed out');
    }

    /**
     * Validate pending authentication state
     */
    async validatePendingAuth(state: string): Promise<boolean> {
        console.log('AuthManager: Validating pending auth for state:', state);

        const pendingAuth = this.context.globalState.get(AuthenticationManager.PENDING_AUTH_KEY) as any;
        console.log('AuthManager: Stored pending auth:', pendingAuth);

        if (!pendingAuth || pendingAuth.state !== state) {
            console.log('AuthManager: State validation failed - no pending auth or state mismatch');
            return false;
        }

        // Check if the auth request is not too old (10 minutes)
        const maxAge = 10 * 60 * 1000;
        const age = Date.now() - pendingAuth.timestamp;
        console.log('AuthManager: Auth request age:', age, 'ms, max age:', maxAge, 'ms');

        if (age > maxAge) {
            console.log('AuthManager: Auth request too old, clearing');
            await this.context.globalState.update(AuthenticationManager.PENDING_AUTH_KEY, undefined);
            return false;
        }

        console.log('AuthManager: State validation successful');
        return true;
    }

    /**
     * Load authentication state from storage
     */
    private loadAuthState(): void {
        const savedState = this.context.globalState.get(AuthenticationManager.AUTH_STATE_KEY) as AuthState;
        if (savedState) {
            this.authState = savedState;
            
            // Check if token is expired
            if (!this.isAuthenticated()) {
                this.clearAuthState();
            }
        }
    }

    /**
     * Save authentication state to storage
     */
    private async saveAuthState(): Promise<void> {
        await this.context.globalState.update(AuthenticationManager.AUTH_STATE_KEY, this.authState);
    }

    /**
     * Clear authentication state
     */
    private clearAuthState(): void {
        this.authState = { isAuthenticated: false };
    }

    /**
     * Dispose resources
     */
    dispose(): void {
        this.authChangeEmitter.dispose();
    }
}
