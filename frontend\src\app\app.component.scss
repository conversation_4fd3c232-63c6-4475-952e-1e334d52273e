/* Enhanced App Layout */
.app-container {
  height: 100vh;
  overflow: hidden;
  background: var(--spt-bg-primary);
}

.doc-container {
  height: 100vh;
  background: var(--spt-bg-primary);
}

.login-layout {
  height: 100vh;
  background: var(--spt-bg-primary);
}

.authenticated-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--spt-bg-primary);
}

.main-content {
  flex: 1;
  overflow-y: auto;
  background: var(--spt-bg-secondary);
  min-height: 0; /* Important for flex child scrolling */
}

/* Responsive Design */
@media (max-width: 768px) {
  .authenticated-layout {
    flex-direction: column;
  }
}
