# Blockchain SPT - Security Scanner Tool
# Standard .gitignore for Go backend + VS Code extension project

# ============================================================================
# Go / Backend
# ============================================================================

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
backend/bin/
backend/build/
backend/dist/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
coverage.txt
coverage.html

# Go workspace file
go.work
go.work.sum

# Dependency directories
vendor/

# Go module cache
go.sum.backup

# Air live reload tool
tmp/
.air.toml

# ============================================================================
# Node.js / VS Code Extension
# ============================================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# VS Code extension build outputs
vscode-extension/out/
vscode-extension/dist/
vscode-extension/*.vsix

# ============================================================================
# Environment & Configuration
# ============================================================================

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env.staging

# Configuration files with secrets
config/local.json
config/production.json
config/secrets.json
*.secret
*.key
*.pem
*.p12
*.pfx

# Database
*.db
*.sqlite
*.sqlite3

# ============================================================================
# IDE & Editor
# ============================================================================

# Visual Studio Code
.vscode/settings.json
.vscode/tasks.json
.vscode/launch.json
.vscode/extensions.json
.vscode/*.code-snippets

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

# ============================================================================
# Operating System
# ============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# Logs & Temporary Files
# ============================================================================

# Log files
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Temporary directories
tmp/
temp/
.tmp/
.temp/

# ============================================================================
# Security & Secrets
# ============================================================================

# Private keys and certificates
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.der

# SSH keys
id_rsa
id_dsa
id_ecdsa
id_ed25519
*.pub

# GPG keys
*.gpg
*.asc

# API keys and tokens
.secrets
secrets.json
credentials.json

# ============================================================================
# Build & Distribution
# ============================================================================

# Build outputs
build/
dist/
out/
target/

# Package files
*.tar.gz
*.zip
*.rar
*.7z

# ============================================================================
# Testing
# ============================================================================

# Test results
test-results/
coverage/
.nyc_output/
junit.xml
TEST-*.xml

# ============================================================================
# Documentation
# ============================================================================

# Generated documentation
docs/build/
site/

# ============================================================================
# Project Specific
# ============================================================================

# SPT Scanner specific
backend/logs/
backend/tmp/
backend/data/
vscode-extension/logs/

# Scan results (if stored locally)
scan-results/
*.scan
*.report

# Development databases
dev.db
test.db

# Local configuration overrides
local.config.json
dev.config.json

frontend/.angular/
frontend/dist/
