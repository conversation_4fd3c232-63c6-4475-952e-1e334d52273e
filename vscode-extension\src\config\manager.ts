import * as vscode from 'vscode';

export interface SPTConfiguration {
    enabled: boolean;
    serverUrl: string;
    webPortalUrl: string;
    apiKey: string;
    autoScan: boolean;
    scanOnOpen: boolean;
    chains: string[];
    severity: string;
    showInlineDecorations: boolean;
    showProblems: boolean;
    enableCodeLens: boolean;
    enableHover: boolean;
}

export class ConfigurationManager {
    private static readonly SECTION = 'spt';
    private configuration: vscode.WorkspaceConfiguration;

    constructor() {
        this.configuration = vscode.workspace.getConfiguration(ConfigurationManager.SECTION);
    }

    getConfig<T = any>(key: keyof SPTConfiguration): T {
        return this.configuration.get<T>(key as string) as T;
    }

    async setConfig<T>(key: keyof SPTConfiguration, value: T): Promise<void> {
        await this.configuration.update(key as string, value, vscode.ConfigurationTarget.Workspace);
    }

    getAllConfig(): SPTConfiguration {
        return {
            enabled: this.getConfig('enabled'),
            serverUrl: this.getConfig('serverUrl'),
            webPortalUrl: this.getConfig('webPortalUrl'),
            apiKey: this.getConfig('apiKey'),
            autoScan: this.getConfig('autoScan'),
            scanOnOpen: this.getConfig('scanOnOpen'),
            chains: this.getConfig('chains'),
            severity: this.getConfig('severity'),
            showInlineDecorations: this.getConfig('showInlineDecorations'),
            showProblems: this.getConfig('showProblems'),
            enableCodeLens: this.getConfig('enableCodeLens'),
            enableHover: this.getConfig('enableHover')
        };
    }

    refresh(): void {
        this.configuration = vscode.workspace.getConfiguration(ConfigurationManager.SECTION);
    }

    isEnabled(): boolean {
        return this.getConfig('enabled');
    }

    getServerUrl(): string {
        return this.getConfig('serverUrl') || 'http://localhost:8080';
    }

    getWebPortalUrl(): string {
        return this.getConfig('webPortalUrl') || 'http://localhost:4200';
    }



    getChains(): string[] {
        return this.getConfig('chains') || ['ethereum', 'bitcoin', 'general'];
    }

    getMinimumSeverity(): string {
        return this.getConfig('severity') || 'medium';
    }

    shouldAutoScan(): boolean {
        return this.getConfig('autoScan');
    }

    shouldScanOnOpen(): boolean {
        return this.getConfig('scanOnOpen');
    }

    shouldShowInlineDecorations(): boolean {
        return this.getConfig('showInlineDecorations');
    }

    shouldShowProblems(): boolean {
        return this.getConfig('showProblems');
    }

    isCodeLensEnabled(): boolean {
        return this.getConfig('enableCodeLens');
    }

    isHoverEnabled(): boolean {
        return this.getConfig('enableHover');
    }

    // Validate configuration
    validateConfig(): { valid: boolean; errors: string[] } {
        const errors: string[] = [];
        const config = this.getAllConfig();

        // Validate server URL
        if (!config.serverUrl) {
            errors.push('Server URL is required');
        } else {
            try {
                new URL(config.serverUrl);
            } catch {
                errors.push('Invalid server URL format');
            }
        }

        // Validate web portal URL
        if (!config.webPortalUrl) {
            errors.push('Web Portal URL is required');
        } else {
            try {
                new URL(config.webPortalUrl);
            } catch {
                errors.push('Invalid web portal URL format');
            }
        }

        // Validate chains
        if (!config.chains || config.chains.length === 0) {
            errors.push('At least one blockchain chain must be selected');
        }

        const validChains = ['ethereum', 'bitcoin', 'general'];
        const invalidChains = config.chains.filter(chain => !validChains.includes(chain));
        if (invalidChains.length > 0) {
            errors.push(`Invalid chains: ${invalidChains.join(', ')}`);
        }

        // Validate severity
        const validSeverities = ['critical', 'high', 'medium', 'low', 'info'];
        if (!validSeverities.includes(config.severity)) {
            errors.push('Invalid severity level');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    // Get configuration for display
    getConfigurationSummary(): string {
        const config = this.getAllConfig();
        return `
            SPT Configuration:
            - Enabled: ${config.enabled}
            - Server URL: ${config.serverUrl}
            - Web Portal URL: ${config.webPortalUrl}
            - API Key: ${config.apiKey ? '***configured***' : 'not set'}
            - Auto Scan: ${config.autoScan}
            - Scan on Open: ${config.scanOnOpen}
            - Chains: ${config.chains.join(', ')}
            - Minimum Severity: ${config.severity}
            - Inline Decorations: ${config.showInlineDecorations}
            - Show Problems: ${config.showProblems}
            - CodeLens: ${config.enableCodeLens}
            - Hover: ${config.enableHover}
                    `.trim();
    }

    // Reset to defaults
    async resetToDefaults(): Promise<void> {
        const defaultConfig: Partial<SPTConfiguration> = {
            enabled: true,
            serverUrl: 'http://localhost:8080',
            webPortalUrl: 'http://localhost:4200',
            apiKey: '',
            autoScan: true,
            scanOnOpen: false,
            chains: ['ethereum', 'bitcoin', 'general'],
            severity: 'medium',
            showInlineDecorations: true,
            showProblems: true,
            enableCodeLens: true,
            enableHover: true
        };

        for (const [key, value] of Object.entries(defaultConfig)) {
            await this.setConfig(key as keyof SPTConfiguration, value);
        }
    }

    // Import configuration from object
    async importConfig(config: Partial<SPTConfiguration>): Promise<void> {
        for (const [key, value] of Object.entries(config)) {
            if (value !== undefined) {
                await this.setConfig(key as keyof SPTConfiguration, value);
            }
        }
    }

    // Export configuration to object
    exportConfig(): SPTConfiguration {
        return this.getAllConfig();
    }

    // Check if configuration has changed
    hasConfigChanged(previousConfig: SPTConfiguration): boolean {
        const currentConfig = this.getAllConfig();
        return JSON.stringify(currentConfig) !== JSON.stringify(previousConfig);
    }

    // Get workspace-specific configuration file path
    getWorkspaceConfigPath(): string | undefined {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            return vscode.Uri.joinPath(workspaceFolder.uri, '.vscode', 'spt.json').fsPath;
        }
        return undefined;
    }

    // Save configuration to workspace file
    async saveToWorkspace(): Promise<void> {
        const configPath = this.getWorkspaceConfigPath();
        if (!configPath) {
            throw new Error('No workspace folder available');
        }

        const config = this.getAllConfig();
        const configJson = JSON.stringify(config, null, 2);
        
        const uri = vscode.Uri.file(configPath);
        await vscode.workspace.fs.writeFile(uri, Buffer.from(configJson, 'utf8'));
        
        vscode.window.showInformationMessage(`Configuration saved to ${configPath}`);
    }

    // Load configuration from workspace file
    async loadFromWorkspace(): Promise<void> {
        const configPath = this.getWorkspaceConfigPath();
        if (!configPath) {
            throw new Error('No workspace folder available');
        }

        try {
            const uri = vscode.Uri.file(configPath);
            const configData = await vscode.workspace.fs.readFile(uri);
            const config = JSON.parse(configData.toString()) as Partial<SPTConfiguration>;
            
            await this.importConfig(config);
            vscode.window.showInformationMessage(`Configuration loaded from ${configPath}`);
        } catch (error) {
            throw new Error(`Failed to load configuration: ${error}`);
        }
    }
}
