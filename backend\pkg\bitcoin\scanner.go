package bitcoin

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/config"
	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
)

// Scanner represents the Bitcoin security scanner
type Scanner struct {
	config         *config.Config
	logger         *logrus.Logger
	scriptAnalyzer *ScriptAnalyzer
	utxoAnalyzer   *UTXOAnalyzer
	walletAnalyzer *WalletAnalyzer
}

// NewScanner creates a new Bitcoin scanner instance
func NewScanner(cfg *config.Config) (*Scanner, error) {
	return &Scanner{
		config:         cfg,
		logger:         logrus.New(),
		scriptAnalyzer: NewScriptAnalyzer(),
		utxoAnalyzer:   NewUTXOAnalyzer(),
		walletAnalyzer: NewWalletAnalyzer(),
	}, nil
}

// ScanProject scans an entire project for Bitcoin-specific security issues
func (s *Scanner) ScanProject(ctx context.Context, projectPath string) ([]models.SecurityIssue, error) {
	s.logger.Infof("Starting Bitcoin security scan for project: %s", projectPath)

	var allIssues []models.SecurityIssue

	// Find all relevant files (JavaScript, TypeScript, Python, etc.)
	files, err := s.findBitcoinFiles(projectPath)
	if err != nil {
		return nil, fmt.Errorf("failed to find Bitcoin-related files: %w", err)
	}

	// Scan each file
	for _, file := range files {
		issues, err := s.ScanFile(ctx, file)
		if err != nil {
			s.logger.Errorf("Failed to scan file %s: %v", file, err)
			continue
		}
		allIssues = append(allIssues, issues...)
	}

	s.logger.Infof("Bitcoin scan completed, found %d issues", len(allIssues))
	return allIssues, nil
}

// ScanFile scans a specific file for Bitcoin security issues
func (s *Scanner) ScanFile(ctx context.Context, filePath string) ([]models.SecurityIssue, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	var issues []models.SecurityIssue

	// Use advanced analyzers
	scriptIssues, err := s.scriptAnalyzer.AnalyzeScript(filePath, string(content))
	if err != nil {
		s.logger.Warnf("Script analysis failed: %v", err)
	} else {
		issues = append(issues, scriptIssues...)
	}

	utxoIssues, err := s.utxoAnalyzer.AnalyzeUTXOs(filePath, string(content))
	if err != nil {
		s.logger.Warnf("UTXO analysis failed: %v", err)
	} else {
		issues = append(issues, utxoIssues...)
	}

	walletIssues, err := s.walletAnalyzer.AnalyzeWallet(filePath, string(content))
	if err != nil {
		s.logger.Warnf("Wallet analysis failed: %v", err)
	} else {
		issues = append(issues, walletIssues...)
	}

	// Run legacy security checks as fallback
	issues = append(issues, s.checkPrivateKeyExposure(filePath, string(content))...)
	issues = append(issues, s.checkMultisigSecurity(filePath, string(content))...)
	issues = append(issues, s.checkUTXOPatterns(filePath, string(content))...)
	issues = append(issues, s.checkScriptValidation(filePath, string(content))...)
	issues = append(issues, s.checkWalletSecurity(filePath, string(content))...)
	issues = append(issues, s.checkNetworkSecurity(filePath, string(content))...)

	return issues, nil
}

// checkPrivateKeyExposure checks for exposed Bitcoin private keys
func (s *Scanner) checkPrivateKeyExposure(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Bitcoin private key patterns (WIF format, hex format)
	patterns := []struct {
		regex       *regexp.Regexp
		description string
	}{
		{
			regex:       regexp.MustCompile(`[5KL][1-9A-HJ-NP-Za-km-z]{50,51}`),
			description: "Bitcoin WIF private key",
		},
		{
			regex:       regexp.MustCompile(`[0-9a-fA-F]{64}`),
			description: "Potential Bitcoin private key (hex format)",
		},
		{
			regex:       regexp.MustCompile(`(?i)(privateKey|private_key|privkey)\s*[:=]\s*["'][^"']+["']`),
			description: "Private key assignment",
		},
	}

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		for _, pattern := range patterns {
			if pattern.regex.MatchString(line) && !strings.Contains(line, "//") && !strings.Contains(line, "#") {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("btc_privkey_%s_%d", filepath.Base(filePath), i+1),
					Type:        "private_key_exposure",
					Severity:    "critical",
					Title:       "Bitcoin Private Key Exposure",
					Description: fmt.Sprintf("Potential %s found in code", pattern.description),
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "bitcoin",
					Category:    "wallet",
					CWE:         "CWE-798",
					OWASP:       "A02:2021 – Cryptographic Failures",
					Suggestion:  "Remove private keys from code and use secure key management",
					References:  []string{"https://bitcoin.org/en/secure-your-wallet"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkMultisigSecurity checks for multisig security issues
func (s *Scanner) checkMultisigSecurity(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Patterns for multisig operations
	multisigPatterns := []struct {
		regex       *regexp.Regexp
		severity    string
		description string
		suggestion  string
	}{
		{
			regex:       regexp.MustCompile(`(?i)createMultisig\(1,`),
			severity:    "high",
			description: "1-of-N multisig provides no additional security",
			suggestion:  "Use at least 2-of-N multisig for meaningful security",
		},
		{
			regex:       regexp.MustCompile(`(?i)multisig.*redeemScript.*length\s*<\s*520`),
			severity:    "medium",
			description: "Multisig redeem script size validation",
			suggestion:  "Ensure redeem script is under 520 bytes limit",
		},
		{
			regex:       regexp.MustCompile(`(?i)(addMultisigAddress|createMultisig).*\[\s*\]`),
			severity:    "high",
			description: "Empty public key array in multisig",
			suggestion:  "Provide valid public keys for multisig creation",
		},
	}

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		for _, pattern := range multisigPatterns {
			if pattern.regex.MatchString(line) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("btc_multisig_%s_%d", filepath.Base(filePath), i+1),
					Type:        "multisig_security",
					Severity:    pattern.severity,
					Title:       "Multisig Security Issue",
					Description: pattern.description,
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "bitcoin",
					Category:    "wallet",
					Suggestion:  pattern.suggestion,
					References:  []string{"https://bitcoin.org/en/developer-guide#multisig"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkUTXOPatterns checks for UTXO handling security issues
func (s *Scanner) checkUTXOPatterns(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// UTXO-related security patterns
	utxoPatterns := []struct {
		regex       *regexp.Regexp
		severity    string
		description string
		suggestion  string
	}{
		{
			regex:       regexp.MustCompile(`(?i)listUnspent\(\)\s*\[0\]`),
			severity:    "medium",
			description: "Using first UTXO without validation",
			suggestion:  "Validate UTXO properties before use",
		},
		{
			regex:       regexp.MustCompile(`(?i)sendToAddress.*amount.*balance`),
			severity:    "high",
			description: "Sending entire balance without fee consideration",
			suggestion:  "Always account for transaction fees",
		},
		{
			regex:       regexp.MustCompile(`(?i)createRawTransaction.*\[\].*\[\]`),
			severity:    "high",
			description: "Creating transaction with empty inputs/outputs",
			suggestion:  "Ensure valid inputs and outputs for transactions",
		},
	}

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		for _, pattern := range utxoPatterns {
			if pattern.regex.MatchString(line) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("btc_utxo_%s_%d", filepath.Base(filePath), i+1),
					Type:        "utxo_security",
					Severity:    pattern.severity,
					Title:       "UTXO Handling Issue",
					Description: pattern.description,
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "bitcoin",
					Category:    "wallet",
					Suggestion:  pattern.suggestion,
					References:  []string{"https://bitcoin.org/en/developer-guide#transactions"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkScriptValidation checks for Bitcoin script security issues
func (s *Scanner) checkScriptValidation(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Script validation patterns
	scriptPatterns := []struct {
		regex       *regexp.Regexp
		severity    string
		description string
		suggestion  string
	}{
		{
			regex:       regexp.MustCompile(`(?i)OP_RETURN.*length\s*>\s*80`),
			severity:    "medium",
			description: "OP_RETURN data exceeds 80 bytes",
			suggestion:  "Keep OP_RETURN data under 80 bytes for standard compliance",
		},
		{
			regex:       regexp.MustCompile(`(?i)scriptSig.*OP_CHECKSIG.*without.*validation`),
			severity:    "high",
			description: "Script signature without proper validation",
			suggestion:  "Always validate script signatures properly",
		},
		{
			regex:       regexp.MustCompile(`(?i)OP_EVAL|OP_CAT|OP_SUBSTR`),
			severity:    "high",
			description: "Usage of disabled Bitcoin opcodes",
			suggestion:  "Avoid using disabled opcodes in Bitcoin scripts",
		},
	}

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		for _, pattern := range scriptPatterns {
			if pattern.regex.MatchString(line) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("btc_script_%s_%d", filepath.Base(filePath), i+1),
					Type:        "script_validation",
					Severity:    pattern.severity,
					Title:       "Bitcoin Script Issue",
					Description: pattern.description,
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "bitcoin",
					Category:    "smart_contract",
					Suggestion:  pattern.suggestion,
					References:  []string{"https://bitcoin.org/en/developer-reference#opcodes"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkWalletSecurity checks for wallet security issues
func (s *Scanner) checkWalletSecurity(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Wallet security patterns
	walletPatterns := []struct {
		regex       *regexp.Regexp
		severity    string
		description string
		suggestion  string
	}{
		{
			regex:       regexp.MustCompile(`(?i)walletpassphrase.*"".*0`),
			severity:    "critical",
			description: "Wallet unlocked with empty passphrase indefinitely",
			suggestion:  "Use strong passphrase and limited unlock time",
		},
		{
			regex:       regexp.MustCompile(`(?i)dumpwallet|dumpprivkey`),
			severity:    "high",
			description: "Wallet private key dumping in code",
			suggestion:  "Avoid dumping private keys in production code",
		},
		{
			regex:       regexp.MustCompile(`(?i)importprivkey.*false`),
			severity:    "medium",
			description: "Importing private key without rescan",
			suggestion:  "Consider rescanning blockchain when importing keys",
		},
	}

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		for _, pattern := range walletPatterns {
			if pattern.regex.MatchString(line) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("btc_wallet_%s_%d", filepath.Base(filePath), i+1),
					Type:        "wallet_security",
					Severity:    pattern.severity,
					Title:       "Wallet Security Issue",
					Description: pattern.description,
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "bitcoin",
					Category:    "wallet",
					Suggestion:  pattern.suggestion,
					References:  []string{"https://bitcoin.org/en/secure-your-wallet"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkNetworkSecurity checks for network-related security issues
func (s *Scanner) checkNetworkSecurity(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Network security patterns
	networkPatterns := []struct {
		regex       *regexp.Regexp
		severity    string
		description string
		suggestion  string
	}{
		{
			regex:       regexp.MustCompile(`(?i)rpcuser.*admin|rpcpassword.*password`),
			severity:    "critical",
			description: "Weak RPC credentials",
			suggestion:  "Use strong, unique RPC credentials",
		},
		{
			regex:       regexp.MustCompile(`(?i)rpcallowip.*0\.0\.0\.0`),
			severity:    "high",
			description: "RPC access allowed from any IP",
			suggestion:  "Restrict RPC access to specific IP addresses",
		},
		{
			regex:       regexp.MustCompile(`(?i)testnet.*false.*mainnet`),
			severity:    "medium",
			description: "Potential mainnet/testnet configuration issue",
			suggestion:  "Clearly separate mainnet and testnet configurations",
		},
	}

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		for _, pattern := range networkPatterns {
			if pattern.regex.MatchString(line) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("btc_network_%s_%d", filepath.Base(filePath), i+1),
					Type:        "network_security",
					Severity:    pattern.severity,
					Title:       "Network Security Issue",
					Description: pattern.description,
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "bitcoin",
					Category:    "environment",
					Suggestion:  pattern.suggestion,
					References:  []string{"https://bitcoin.org/en/full-node#network-configuration"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// findBitcoinFiles finds all Bitcoin-related files in the project
func (s *Scanner) findBitcoinFiles(projectPath string) ([]string, error) {
	var files []string
	extensions := []string{".js", ".ts", ".py", ".go", ".json", ".conf"}

	err := filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip entire directories that should be excluded
		if info.IsDir() {
			for _, excludePath := range s.config.Scanning.Paths.Exclude {
				if info.Name() == excludePath || strings.Contains(path, excludePath) {
					s.logger.Debugf("Skipping directory: %s", path)
					return filepath.SkipDir
				}
			}

			// Skip scanner source code directories to avoid false positives
			if s.isScannerSourcePath(path) {
				s.logger.Debugf("Skipping scanner source directory: %s", path)
				return filepath.SkipDir
			}

			return nil
		}

		// Skip hidden files
		if strings.HasPrefix(info.Name(), ".") {
			return nil
		}

		// Check if file has relevant extension
		for _, ext := range extensions {
			if strings.HasSuffix(path, ext) {
				// Check if path should be excluded
				for _, excludePath := range s.config.Scanning.Paths.Exclude {
					if strings.Contains(path, excludePath) {
						return nil
					}
				}
				files = append(files, path)
				break
			}
		}

		return nil
	})

	return files, err
}

// isScannerSourcePath checks if a path is part of the scanner source code
func (s *Scanner) isScannerSourcePath(path string) bool {
	scannerPaths := []string{
		"backend/pkg/bitcoin",
		"backend/pkg/ethereum",
		"backend/pkg/security",
		"backend/pkg/dependencies",
		"backend/pkg/scanner",
		"backend/pkg/models",
		"backend/pkg/api",
		"backend/pkg/database",
		"backend/cmd",
		"backend/internal",
	}

	for _, scannerPath := range scannerPaths {
		if strings.Contains(path, scannerPath) {
			return true
		}
	}

	return false
}
