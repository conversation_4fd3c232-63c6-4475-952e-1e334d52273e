# Blockchain Security Protocol Tool (SPT) Makefile

.PHONY: help build test clean install dev setup backend frontend cli docker

# Default target
help:
	@echo "Blockchain Security Protocol Tool (SPT) - Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  setup     - Initial project setup"
	@echo "  build     - Build all components"
	@echo "  backend   - Build and run backend server"
	@echo "  frontend  - Build and run frontend"
	@echo "  cli       - Build CLI tool"
	@echo "  test      - Run all tests"
	@echo "  clean     - Clean build artifacts"
	@echo "  install   - Install dependencies"
	@echo "  dev       - Start development environment"
	@echo "  docker    - Build Docker containers"
	@echo "  help      - Show this help message"

# Initial project setup
setup:
	@echo "🚀 Setting up SPT development environment..."
	@echo "📦 Installing Go dependencies..."
	cd backend && go mod tidy
	cd cli && go mod tidy
	@echo "📦 Installing Node.js dependencies..."
	cd frontend && npm install
	@echo "✅ Setup complete!"

# Build all components
build: build-backend build-frontend build-cli
	@echo "✅ All components built successfully!"

# Build backend
build-backend:
	@echo "🔨 Building backend..."
	cd backend && go build -o bin/spt-server cmd/main.go

# Build frontend
build-frontend:
	@echo "🔨 Building frontend..."
	cd frontend && npm run build

# Build CLI
build-cli:
	@echo "🔨 Building CLI..."
	cd cli && go build -o bin/spt main.go

# Run backend server
backend:
	@echo "🚀 Starting backend server..."
	cd backend && go run cmd/main.go

# Run frontend development server
frontend:
	@echo "🚀 Starting frontend development server..."
	cd frontend && npm start

# Build CLI tool
cli:
	@echo "🔧 Building CLI tool..."
	@mkdir -p build
	go build -ldflags "-X main.version=1.0.0 -X main.commit=$(shell git rev-parse --short HEAD) -X main.date=$(shell date -u +%Y-%m-%dT%H:%M:%SZ)" -o build/spt cmdmain.go
	@echo "✅ CLI tool built: build/spt"
	@echo "Usage: ./build/spt --help"

# CLI install target
cli-install:
	@echo "📦 Installing CLI..."
	go install ./cmd/spt

cli-clean:
	@echo "🧹 Cleaning CLI artifacts..."
	@rm -f build/spt

# Install dependencies
install:
	@echo "📦 Installing dependencies..."
	cd backend && go mod download
	cd cli && go mod download
	cd frontend && npm ci

# Run tests
test: test-backend test-frontend
	@echo "✅ All tests completed!"

# Test backend
test-backend:
	@echo "🧪 Running backend tests..."
	cd backend && go test ./...

# Test frontend
test-frontend:
	@echo "🧪 Running frontend tests..."
	cd frontend && npm test -- --watch=false --browsers=ChromeHeadless

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -rf backend/bin/
	rm -rf cli/bin/
	rm -rf frontend/dist/
	rm -rf frontend/node_modules/.cache/
	@echo "✅ Clean complete!"



# Docker build
docker:
	@echo "🐳 Building Docker containers..."
	docker build -t spt-backend -f docker/Dockerfile.backend .
	docker build -t spt-frontend -f docker/Dockerfile.frontend .
	@echo "✅ Docker containers built!"

# Production build
prod-build:
	@echo "🏭 Building for production..."
	cd frontend && npm run build --prod
	cd backend && CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/spt-server cmd/main.go
	cd cli && CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/spt main.go
	@echo "✅ Production build complete!"

# Lint code
lint:
	@echo "🔍 Linting code..."
	cd backend && go vet ./...
	cd backend && golangci-lint run
	cd frontend && npm run lint
	@echo "✅ Linting complete!"

# Format code
format:
	@echo "✨ Formatting code..."
	cd backend && go fmt ./...
	cd cli && go fmt ./...
	cd frontend && npm run format
	@echo "✅ Code formatted!"

# Security scan
security-scan:
	@echo "🔒 Running security scan..."
	cd backend && gosec ./...
	cd frontend && npm audit
	@echo "✅ Security scan complete!"

# Generate documentation
docs:
	@echo "📚 Generating documentation..."
	cd backend && godoc -http=:6060 &
	@echo "Backend docs available at: http://localhost:6060"
	@echo "Frontend docs: see docs/ directory"

# Database migration (if using database)
migrate:
	@echo "🗄️ Running database migrations..."
	cd backend && go run cmd/migrate.go

# Version information
version:
	@echo "SPT Version Information:"
	@echo "Go version: $(shell go version)"
	@echo "Node version: $(shell node --version)"
	@echo "NPM version: $(shell npm --version)"

# Quick development setup
quick-start: setup build
	@echo "🎉 Quick start complete!"
	@echo ""
	@echo "To start development:"
	@echo "  make dev"
	@echo ""
	@echo "To run CLI:"
	@echo "  ./cli/bin/spt --help"
	@echo ""
	@echo "To access web interface:"
	@echo "  http://localhost:4200"

# Release build
release: clean prod-build test
	@echo "📦 Creating release package..."
	mkdir -p release
	cp backend/bin/spt-server release/
	cp cli/bin/spt release/
	cp -r frontend/dist release/frontend
	cp README.md release/
	cp spt.config.json release/
	tar -czf release/spt-$(shell date +%Y%m%d).tar.gz -C release .
	@echo "✅ Release package created: release/spt-$(shell date +%Y%m%d).tar.gz"
