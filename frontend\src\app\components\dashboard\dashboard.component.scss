.dashboard-container {
  padding: 0;
  max-width: 100%;
  margin: 0;
  background: var(--spt-bg-secondary);
  min-height: 100vh;
}

/* Enhanced Dashboard Header */
.dashboard-header {
  background: linear-gradient(135deg, var(--spt-primary-600) 0%, var(--spt-secondary-600) 100%);
  color: white;
  padding: var(--spt-space-12) var(--spt-space-8);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spt-space-8);
  border-radius: 0 0 var(--spt-radius-3xl) var(--spt-radius-3xl);
  position: relative;
  overflow: hidden;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-content {
  display: flex;
  gap: var(--spt-space-12);
  align-items: flex-start;
  position: relative;
  z-index: 1;
}

.header-text {
  flex: 1;
}

.dashboard-title {
  margin: 0 0 var(--spt-space-2) 0;
  font-size: var(--spt-text-4xl);
  font-weight: var(--spt-font-bold);
  display: flex;
  align-items: center;
  gap: var(--spt-space-3);
  line-height: var(--spt-leading-tight);
}

.title-icon {
  font-size: 40px;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--spt-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: var(--spt-text-lg);
  font-weight: var(--spt-font-normal);
  line-height: var(--spt-leading-relaxed);
}

.header-stats {
  display: flex;
  gap: var(--spt-space-6);
  margin-top: var(--spt-space-2);
}

.quick-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spt-space-3) var(--spt-space-4);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--spt-radius-xl);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-value {
  font-size: var(--spt-text-2xl);
  font-weight: var(--spt-font-bold);
  line-height: 1;
}

.stat-label {
  font-size: var(--spt-text-xs);
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: var(--spt-space-1);
}

.header-actions {
  display: flex;
  gap: var(--spt-space-3);
  position: relative;
  z-index: 1;
}

.action-button {
  height: 48px;
  padding: 0 var(--spt-space-6);
  border-radius: var(--spt-radius-xl);
  font-weight: var(--spt-font-semibold);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-button.primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.action-button.primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: var(--spt-shadow-lg);
}

.action-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.action-button.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Enhanced Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spt-space-6);
  margin: 0 var(--spt-space-8) var(--spt-space-12) var(--spt-space-8);
}

.stat-card {
  border-radius: var(--spt-radius-2xl);
  border: 1px solid var(--spt-border);
  background: var(--spt-surface);
  box-shadow: var(--spt-shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.stat-card.elevated {
  box-shadow: var(--spt-shadow-lg);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--spt-shadow-xl);
  border-color: var(--spt-primary-200);
}

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spt-space-5) var(--spt-space-6) var(--spt-space-3) var(--spt-space-6);
}

.stat-card-content {
  padding: 0 var(--spt-space-6) var(--spt-space-4) var(--spt-space-6);
}

.stat-card-footer {
  padding: var(--spt-space-3) var(--spt-space-6) var(--spt-space-5) var(--spt-space-6);
  border-top: 1px solid var(--spt-border-light);
  background: var(--spt-bg-secondary);
}

.stat-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: var(--spt-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.stat-icon-wrapper.success {
  background: linear-gradient(135deg, var(--spt-success-100) 0%, var(--spt-success-200) 100%);
  border: 1px solid var(--spt-success-300);
}

.stat-icon-wrapper.critical {
  background: linear-gradient(135deg, var(--spt-error-100) 0%, var(--spt-error-200) 100%);
  border: 1px solid var(--spt-error-300);
}

.stat-icon-wrapper.warning {
  background: linear-gradient(135deg, var(--spt-warning-100) 0%, var(--spt-warning-200) 100%);
  border: 1px solid var(--spt-warning-300);
}

.stat-icon-wrapper.info {
  background: linear-gradient(135deg, var(--spt-info-100) 0%, var(--spt-info-200) 100%);
  border: 1px solid var(--spt-info-300);
}

.stat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.stat-card:hover .stat-icon {
  transform: scale(1.1);
}

.stat-icon-wrapper.success .stat-icon { color: var(--spt-success-700); }
.stat-icon-wrapper.critical .stat-icon { color: var(--spt-error-700); }
.stat-icon-wrapper.warning .stat-icon { color: var(--spt-warning-700); }
.stat-icon-wrapper.info .stat-icon { color: var(--spt-info-700); }

.stat-trend {
  display: flex;
  align-items: center;
  gap: var(--spt-space-1);
  padding: var(--spt-space-1) var(--spt-space-2);
  border-radius: var(--spt-radius-lg);
  font-size: var(--spt-text-xs);
  font-weight: var(--spt-font-semibold);
}

.trend-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.trend-icon.positive { color: var(--spt-success-600); }
.trend-icon.negative { color: var(--spt-error-600); }
.trend-icon.neutral { color: var(--spt-text-tertiary); }

.trend-value {
  font-weight: var(--spt-font-bold);
}

.stat-number {
  font-size: var(--spt-text-4xl);
  font-weight: var(--spt-font-bold);
  color: var(--spt-text-primary);
  margin-bottom: var(--spt-space-1);
  line-height: var(--spt-leading-none);
  transition: color 0.3s ease;
}

.stat-number.critical { color: var(--spt-error-600); }
.stat-number.warning { color: var(--spt-warning-600); }
.stat-number.info { color: var(--spt-info-600); }

.stat-label {
  font-size: var(--spt-text-base);
  color: var(--spt-text-primary);
  font-weight: var(--spt-font-semibold);
  margin-bottom: var(--spt-space-1);
  line-height: var(--spt-leading-tight);
}

.stat-description {
  font-size: var(--spt-text-sm);
  color: var(--spt-text-secondary);
  font-weight: var(--spt-font-normal);
  line-height: var(--spt-leading-relaxed);
}

.stat-card-footer button {
  width: 100%;
  justify-content: flex-start;
  gap: var(--spt-space-2);
  padding: var(--spt-space-2) var(--spt-space-3);
  border-radius: var(--spt-radius-lg);
  font-weight: var(--spt-font-medium);
  transition: all 0.2s ease;
}

.stat-card-footer button:hover {
  transform: translateX(4px);
}

.stat-card-footer button mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* Chart Sections */
.chart-section {
  margin-bottom: var(--spt-space-8);
}

.chart-section:last-of-type {
  margin-bottom: var(--spt-space-6);
}

/* Recent Scans Table */
.recent-scans-card {
  margin: 0 var(--spt-space-8) var(--spt-space-8) var(--spt-space-8);
  border-radius: var(--spt-radius-2xl);
  border: 1px solid var(--spt-border);
  box-shadow: var(--spt-shadow-sm);
  overflow: hidden;
}

.table-container {
  overflow-x: auto;
  max-width: 100%;
}

.scans-table {
  width: 100%;
  background: var(--spt-surface);
}

.scan-id {
  font-family: var(--spt-font-mono);
  font-size: var(--spt-text-sm);
  color: var(--spt-text-secondary);
  background: var(--spt-gray-100);
  padding: var(--spt-space-1) var(--spt-space-2);
  border-radius: var(--spt-radius-md);
}

.project-info {
  display: flex;
  flex-direction: column;
  gap: var(--spt-space-1);
}

.project-name {
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.project-path {
  font-size: var(--spt-text-xs);
  color: var(--spt-text-secondary);
  font-family: var(--spt-font-mono);
}

.chains-list mat-chip-set {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spt-space-1);
}

.status-badge {
  display: flex;
  align-items: center;
  gap: var(--spt-space-2);
  padding: var(--spt-space-2) var(--spt-space-3);
  border-radius: var(--spt-radius-lg);
  font-size: var(--spt-text-sm);
  font-weight: var(--spt-font-medium);
  text-transform: capitalize;
}

.status-badge.status-completed {
  background: var(--spt-success-100);
  color: var(--spt-success-700);
}

.status-badge.status-running {
  background: var(--spt-info-100);
  color: var(--spt-info-700);
}

.status-badge.status-failed {
  background: var(--spt-error-100);
  color: var(--spt-error-700);
}

.status-badge.status-pending {
  background: var(--spt-warning-100);
  color: var(--spt-warning-700);
}

.issues-summary {
  display: flex;
  flex-direction: column;
  gap: var(--spt-space-1);
}

.issue-count {
  font-size: var(--spt-text-xs);
  font-weight: var(--spt-font-semibold);
  padding: var(--spt-space-1) var(--spt-space-2);
  border-radius: var(--spt-radius-md);
}

.issue-count.critical {
  background: var(--spt-error-100);
  color: var(--spt-error-700);
}

.issue-count.high {
  background: var(--spt-warning-100);
  color: var(--spt-warning-700);
}

.issue-count.medium {
  background: var(--spt-info-100);
  color: var(--spt-info-700);
}

.no-issues {
  font-size: var(--spt-text-xs);
  color: var(--spt-text-secondary);
  font-style: italic;
}

.date-info {
  display: flex;
  flex-direction: column;
  gap: var(--spt-space-1);
}

.date {
  font-weight: var(--spt-font-medium);
  color: var(--spt-text-primary);
}

.time {
  font-size: var(--spt-text-xs);
  color: var(--spt-text-secondary);
}

.action-buttons {
  display: flex;
  gap: var(--spt-space-1);
}

.action-buttons button {
  width: 32px;
  height: 32px;
  border-radius: var(--spt-radius-lg);
  color: var(--spt-text-secondary);
  transition: all 0.2s ease;
}

.action-buttons button:hover {
  background: var(--spt-primary-50);
  color: var(--spt-primary-600);
  transform: scale(1.1);
}

/* Quick Actions */
.quick-actions-card {
  margin: 0 var(--spt-space-8) var(--spt-space-8) var(--spt-space-8);
  border-radius: var(--spt-radius-2xl);
  border: 1px solid var(--spt-border);
  box-shadow: var(--spt-shadow-sm);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spt-space-4);
}

.actions-grid button {
  height: 64px;
  border-radius: var(--spt-radius-xl);
  font-weight: var(--spt-font-semibold);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spt-space-3);
  transition: all 0.3s ease;
}

.actions-grid button:hover {
  transform: translateY(-2px);
  box-shadow: var(--spt-shadow-lg);
}

.actions-grid mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

/* Responsive adjustments for charts */
@media (max-width: 768px) {
  .chart-section {
    margin-bottom: var(--spt-space-6);
  }
}
