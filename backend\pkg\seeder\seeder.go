package seeder

import (
	"fmt"
	"os"

	"blockchain-spt/backend/pkg/database"
	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/bcrypt"
)

// Seeder handles database seeding operations
type Seeder struct {
	db     *database.Database
	logger *logrus.Logger
}

// New creates a new seeder instance
func New(db *database.Database) *Seeder {
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{})

	return &Seeder{
		db:     db,
		logger: logger,
	}
}

// SeedDefaultUser creates a default admin user if no users exist
func (s *Seeder) SeedDefaultUser() error {
	if s.db == nil || !s.db.IsConnected() {
		s.logger.Warn("Database not connected, skipping user seeding")
		return nil
	}

	// Check if any users exist
	users, err := s.db.GetAllUsers()
	if err != nil {
		return fmt.Errorf("failed to check existing users: %w", err)
	}

	if len(users) > 0 {
		s.logger.Info("Users already exist, skipping default user creation")
		return nil
	}

	// Create default admin user
	defaultUser := &models.User{
		Username:  getEnvOrDefault("SPT_DEFAULT_USERNAME", "admin"),
		Email:     getEnvOrDefault("SPT_DEFAULT_EMAIL", "<EMAIL>"),
		FirstName: stringPtr("SPT"),
		LastName:  stringPtr("Administrator"),
		Role:      models.UserRoleAdmin,
		IsActive:  true,
	}

	// Set password
	defaultPassword := getEnvOrDefault("SPT_DEFAULT_PASSWORD", "spt123456")
	if err := s.setUserPassword(defaultUser, defaultPassword); err != nil {
		return fmt.Errorf("failed to set user password: %w", err)
	}

	// Create the user
	if err := s.db.CreateUser(defaultUser); err != nil {
		return fmt.Errorf("failed to create default user: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"username": defaultUser.Username,
		"email":    defaultUser.Email,
		"role":     defaultUser.Role,
	}).Info("Default admin user created successfully")

	// Log credentials for first-time setup
	fmt.Println()
	fmt.Println("🔐 Default Admin User Created")
	fmt.Println("============================")
	fmt.Printf("Username: %s\n", defaultUser.Username)
	fmt.Printf("Email:    %s\n", defaultUser.Email)
	fmt.Printf("Password: %s\n", defaultPassword)
	fmt.Printf("Role:     %s\n", defaultUser.Role)
	fmt.Println()
	fmt.Println("⚠️  IMPORTANT: Please change the default password after first login!")
	fmt.Println("============================")
	fmt.Println()

	return nil
}

// SeedDevelopmentUsers creates additional users for development environment
func (s *Seeder) SeedDevelopmentUsers() error {
	if s.db == nil || !s.db.IsConnected() {
		s.logger.Warn("Database not connected, skipping development user seeding")
		return nil
	}

	// Only seed in development environment
	env := getEnvOrDefault("SPT_ENVIRONMENT", "development")
	if env != "development" {
		s.logger.Info("Not in development environment, skipping development user seeding")
		return nil
	}

	developmentUsers := []*models.User{
		{
			Username:  "developer",
			Email:     "<EMAIL>",
			FirstName: stringPtr("John"),
			LastName:  stringPtr("Developer"),
			Role:      models.UserRoleDeveloper,
			IsActive:  true,
		},
		{
			Username:  "viewer",
			Email:     "<EMAIL>",
			FirstName: stringPtr("Jane"),
			LastName:  stringPtr("Viewer"),
			Role:      models.UserRoleViewer,
			IsActive:  true,
		},
	}

	for _, user := range developmentUsers {
		// Check if user already exists
		exists, err := s.db.UserExists(user.Username)
		if err != nil {
			s.logger.WithError(err).Warnf("Failed to check if user %s exists", user.Username)
			continue
		}

		if exists {
			s.logger.Infof("User %s already exists, skipping", user.Username)
			continue
		}

		// Set default password
		if err := s.setUserPassword(user, "password123"); err != nil {
			s.logger.WithError(err).Warnf("Failed to set password for user %s", user.Username)
			continue
		}

		// Create the user
		if err := s.db.CreateUser(user); err != nil {
			s.logger.WithError(err).Warnf("Failed to create user %s", user.Username)
			continue
		}

		s.logger.WithFields(logrus.Fields{
			"username": user.Username,
			"email":    user.Email,
			"role":     user.Role,
		}).Info("Development user created successfully")
	}

	return nil
}

// SeedAll runs all seeding operations
func (s *Seeder) SeedAll() error {
	s.logger.Info("Starting database seeding...")

	// Seed default admin user
	if err := s.SeedDefaultUser(); err != nil {
		return fmt.Errorf("failed to seed default user: %w", err)
	}

	// Seed development users if in development environment
	if err := s.SeedDevelopmentUsers(); err != nil {
		return fmt.Errorf("failed to seed development users: %w", err)
	}

	s.logger.Info("Database seeding completed successfully")
	return nil
}

// setUserPassword hashes and sets the user password
func (s *Seeder) setUserPassword(user *models.User, password string) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	user.Password = string(hashedPassword)
	return nil
}

// Helper functions
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func stringPtr(s string) *string {
	return &s
}
