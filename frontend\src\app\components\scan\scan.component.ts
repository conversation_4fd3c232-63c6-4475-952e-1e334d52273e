import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatStepperModule } from '@angular/material/stepper';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ApiService } from '../../services/api.service';
import { WebSocketService, ScanProgress } from '../../services/websocket.service';
import { ScanRequest } from '../../models/security.models';

@Component({
  selector: 'app-scan',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatSnackBarModule,
    MatStepperModule
  ],
  template: `
    <div class="scan-container">
      <h1>🔍 Security Scan</h1>

      <!-- Scan Progress (shown when scanning) -->
      <mat-card *ngIf="currentScanProgress" class="progress-card">
        <mat-card-header>
          <mat-card-title>Scan in Progress</mat-card-title>
          <mat-card-subtitle>{{ currentScanProgress.message }}</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="progress-info">
            <div class="progress-stats">
              <div class="stat">
                <span class="label">Status:</span>
                <span class="value">{{ currentScanProgress.status | titlecase }}</span>
              </div>
              <div class="stat">
                <span class="label">Files:</span>
                <span class="value">{{ currentScanProgress.filesScanned }} / {{ currentScanProgress.totalFiles }}</span>
              </div>
              <div class="stat">
                <span class="label">Issues Found:</span>
                <span class="value">{{ currentScanProgress.issuesFound }}</span>
              </div>
            </div>
            <mat-progress-bar
              mode="determinate"
              [value]="currentScanProgress.progress"
              class="progress-bar">
            </mat-progress-bar>
            <div class="progress-text">
              {{ currentScanProgress.progress }}% Complete
              <span *ngIf="currentScanProgress.currentFile" class="current-file">
                - {{ currentScanProgress.currentFile }}
              </span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Scan Configuration Form -->
      <mat-card class="scan-form-card" [class.disabled]="isScanning">
        <mat-card-header>
          <mat-card-title>Configure Security Scan</mat-card-title>
          <mat-card-subtitle>Set up a comprehensive security scan for your blockchain project</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="scanForm" (ngSubmit)="onSubmit()">
            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Project Path</mat-label>
                <input matInput formControlName="projectPath" placeholder="/path/to/your/project" [disabled]="isScanning">
                <mat-icon matSuffix>folder</mat-icon>
                <mat-error *ngIf="scanForm.get('projectPath')?.hasError('required')">
                  Project path is required
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Blockchain Chains</mat-label>
                <mat-select formControlName="chains" multiple>
                  <mat-option value="ethereum">Ethereum</mat-option>
                  <mat-option value="bitcoin">Bitcoin</mat-option>
                  <mat-option value="general">General Security</mat-option>
                </mat-select>
                <mat-error *ngIf="scanForm.get('chains')?.hasError('required')">
                  At least one chain must be selected
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Scan Type</mat-label>
                <mat-select formControlName="scanType">
                  <mat-option value="full">Full Scan</mat-option>
                  <mat-option value="quick">Quick Scan</mat-option>
                  <mat-option value="custom">Custom Scan</mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="security-options">
              <h3>Security Check Options</h3>
              <div class="options-grid">
                <mat-checkbox formControlName="checkKeys">
                  <mat-icon>vpn_key</mat-icon>
                  Check for exposed keys
                </mat-checkbox>
                <mat-checkbox formControlName="checkContracts">
                  <mat-icon>code</mat-icon>
                  Smart contract audit
                </mat-checkbox>
                <mat-checkbox formControlName="checkDependencies">
                  <mat-icon>extension</mat-icon>
                  Dependency scanning
                </mat-checkbox>
                <mat-checkbox formControlName="checkEnvironment">
                  <mat-icon>settings</mat-icon>
                  Environment security
                </mat-checkbox>
              </div>
            </div>

            <div class="form-actions">
              <button mat-raised-button type="button" (click)="onReset()">
                <mat-icon>refresh</mat-icon>
                Reset
              </button>
              <button mat-raised-button color="primary" type="submit" [disabled]="scanForm.invalid || isScanning">
                <mat-icon>security</mat-icon>
                {{ isScanning ? 'Scanning...' : 'Start Scan' }}
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Scan Progress -->
      <mat-card *ngIf="isScanning" class="progress-card">
        <mat-card-header>
          <mat-card-title>Scan in Progress</mat-card-title>
          <mat-card-subtitle>{{ scanStatus }}</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <mat-progress-bar mode="indeterminate" color="primary"></mat-progress-bar>
          <div class="progress-details">
            <p>Scanning project: {{ currentScanPath }}</p>
            <p>Chains: {{ currentScanChains.join(', ') }}</p>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Quick Scan Options -->
      <mat-card class="quick-scan-card">
        <mat-card-header>
          <mat-card-title>Quick Scan Options</mat-card-title>
          <mat-card-subtitle>Run specific security checks quickly</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="quick-actions">
            <button mat-stroked-button (click)="quickScanKeys()">
              <mat-icon>vpn_key</mat-icon>
              Check Keys Only
            </button>
            <button mat-stroked-button (click)="quickScanContracts()">
              <mat-icon>code</mat-icon>
              Audit Contracts
            </button>
            <button mat-stroked-button (click)="quickScanEnvironment()">
              <mat-icon>settings</mat-icon>
              Environment Check
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Recent Scans -->
      <mat-card class="recent-scans-card">
        <mat-card-header>
          <mat-card-title>Recent Scans</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="recent-scans-list">
            <div *ngFor="let scan of recentScans" class="scan-item" (click)="viewScanDetails(scan.id)">
              <div class="scan-info">
                <h4>{{ getProjectName(scan.project_path) }}</h4>
                <p>{{ scan.chains.join(', ') }} • {{ formatDate(scan.created_at) }}</p>
              </div>
              <div class="scan-status">
                <span class="status-badge" [class]="'status-' + scan.status">{{ scan.status }}</span>
                <span class="issues-count">{{ scan.issues?.length || 0 }} issues</span>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .scan-container {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }

    h1 {
      margin-bottom: 30px;
      color: #333;
    }

    .scan-form-card,
    .progress-card,
    .quick-scan-card,
    .recent-scans-card {
      margin-bottom: 30px;
    }

    .form-row {
      margin-bottom: 20px;
    }

    .full-width {
      width: 100%;
    }

    .security-options {
      margin: 30px 0;
    }

    .security-options h3 {
      margin-bottom: 15px;
      color: #333;
    }

    .options-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
    }

    .options-grid mat-checkbox {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 15px;
      margin-top: 30px;
    }

    .progress-card {
      border-left: 4px solid #2196f3;
    }

    .progress-details {
      margin-top: 15px;
    }

    .progress-details p {
      margin: 5px 0;
      color: #666;
    }

    .quick-actions {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }

    .quick-actions button {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .recent-scans-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .scan-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .scan-item:hover {
      background-color: #f5f5f5;
    }

    .scan-info h4 {
      margin: 0 0 5px 0;
      color: #333;
    }

    .scan-info p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }

    .scan-status {
      text-align: right;
    }

    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
    }

    .status-completed {
      background-color: #4caf50;
      color: white;
    }

    .status-running {
      background-color: #2196f3;
      color: white;
    }

    .status-failed {
      background-color: #f44336;
      color: white;
    }

    .status-pending {
      background-color: #ff9800;
      color: white;
    }

    .issues-count {
      display: block;
      margin-top: 5px;
      font-size: 12px;
      color: #666;
    }
  `]
})
export class ScanComponent implements OnInit, OnDestroy {
  scanForm: FormGroup;
  isScanning = false;
  scanStatus = '';
  currentScanPath = '';
  currentScanChains: string[] = [];
  recentScans: any[] = [];
  currentScanProgress: ScanProgress | null = null;
  private subscriptions: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private apiService: ApiService,
    private webSocketService: WebSocketService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.scanForm = this.fb.group({
      projectPath: ['', Validators.required],
      chains: [['ethereum'], Validators.required],
      scanType: ['full'],
      checkKeys: [true],
      checkContracts: [true],
      checkDependencies: [true],
      checkEnvironment: [true]
    });
  }

  ngOnInit(): void {
    this.loadRecentScans();
    this.subscribeToScanStatus();
    this.subscribeToScanProgress();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  onSubmit(): void {
    if (this.scanForm.valid) {
      const formValue = this.scanForm.value;
      const scanRequest: ScanRequest = {
        project_path: formValue.projectPath,
        chains: formValue.chains,
        scan_type: formValue.scanType,
        options: {
          check_keys: formValue.checkKeys,
          check_contracts: formValue.checkContracts,
          check_dependencies: formValue.checkDependencies,
          check_environment: formValue.checkEnvironment
        }
      };

      this.startScan(scanRequest);
    }
  }

  startScan(request: ScanRequest): void {
    this.isScanning = true;
    this.currentScanPath = request.project_path;
    this.currentScanChains = request.chains;
    this.scanStatus = 'Initializing scan...';

    this.apiService.startScan(request).subscribe({
      next: (response) => {
        this.snackBar.open('Scan started successfully!', 'Close', { duration: 3000 });
        this.scanStatus = 'Scan in progress...';

        // Subscribe to real-time progress updates for this scan
        this.webSocketService.subscribeScanProgress(response.scan_id);
      },
      error: (error) => {
        this.isScanning = false;
        this.snackBar.open('Scan failed to start', 'Close', { duration: 3000 });
      }
    });
  }

  onReset(): void {
    this.scanForm.reset({
      chains: ['ethereum'],
      scanType: 'full',
      checkKeys: true,
      checkContracts: true,
      checkDependencies: true,
      checkEnvironment: true
    });
  }

  quickScanKeys(): void {
    const request: ScanRequest = {
      project_path: this.scanForm.get('projectPath')?.value || '.',
      chains: ['general'],
      scan_type: 'quick',
      options: { check_keys: true }
    };
    this.startScan(request);
  }

  quickScanContracts(): void {
    const request: ScanRequest = {
      project_path: this.scanForm.get('projectPath')?.value || '.',
      chains: this.scanForm.get('chains')?.value || ['ethereum'],
      scan_type: 'quick',
      options: { check_contracts: true }
    };
    this.startScan(request);
  }

  quickScanEnvironment(): void {
    const request: ScanRequest = {
      project_path: this.scanForm.get('projectPath')?.value || '.',
      chains: ['general'],
      scan_type: 'quick',
      options: { check_environment: true }
    };
    this.startScan(request);
  }

  loadRecentScans(): void {
    this.apiService.getScanHistory().subscribe({
      next: (response) => {
        this.recentScans = response.scans.slice(0, 3);
      },
      error: () => {
        this.recentScans = [];
      }
    });
  }

  subscribeToScanStatus(): void {
    const statusSub = this.apiService.scanStatus$.subscribe(status => {
      if (status !== 'idle') {
        this.scanStatus = status;
      }
    });
    this.subscriptions.push(statusSub);
  }

  subscribeToScanProgress(): void {
    const progressSub = this.webSocketService.scanProgress$.subscribe(progress => {
      this.currentScanProgress = progress;
      if (progress) {
        this.isScanning = progress.status === 'starting' || progress.status === 'scanning' || progress.status === 'analyzing';

        if (progress.status === 'completed') {
          this.snackBar.open('Scan completed successfully!', 'View Results', { duration: 5000 })
            .onAction().subscribe(() => {
              this.router.navigate(['/scan', progress.scanId]);
            });
          this.loadRecentScans(); // Refresh the recent scans list
        } else if (progress.status === 'failed') {
          this.snackBar.open('Scan failed: ' + (progress.message || 'Unknown error'), 'Close', { duration: 5000 });
        }
      }
    });
    this.subscriptions.push(progressSub);
  }

  viewScanDetails(scanId: string): void {
    this.router.navigate(['/scan', scanId]);
  }

  getProjectName(path: string): string {
    return path.split('/').pop() || path;
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }
}
