<!-- Documentation Route - No Authentication Required -->
<div *ngIf="isDocumentationRoute$ | async" class="doc-container">
  <router-outlet></router-outlet>
</div>

<!-- Main App - Authentication Required -->
<div *ngIf="!(isDocumentationRoute$ | async)" class="app-container">
  <!-- Authenticated Layout -->
  <div *ngIf="currentUser$ | async as user; else loginView" class="authenticated-layout">
    <!-- Enhanced Navigation -->
    <app-navigation></app-navigation>
    
    <!-- Main Content Area -->
    <main class="main-content">
      <router-outlet></router-outlet>
    </main>
  </div>

  <!-- Login View -->
  <ng-template #loginView>
    <div class="login-layout">
      <router-outlet></router-outlet>
    </div>
  </ng-template>
</div>
