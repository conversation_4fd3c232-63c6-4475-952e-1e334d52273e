import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { ApiService } from '../../services/api.service';
import { ScanResult, SecurityIssue, SEVERITY_COLORS } from '../../models/security.models';
import { SecurityMetricsChartComponent, SecurityMetric } from '../../shared/charts/security-metrics-chart/security-metrics-chart.component';
import { ScanProgressChartComponent, ScanProgress } from '../../shared/charts/scan-progress-chart/scan-progress-chart.component';
import { SecurityTrendsDashboardComponent, SecurityTrend } from '../../shared/charts/security-trends-dashboard/security-trends-dashboard.component';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatTableModule,
    MatChipsModule,
    MatProgressBarModule,
    MatTooltipModule,
    RouterModule,
    SecurityMetricsChartComponent,
    ScanProgressChartComponent,
    SecurityTrendsDashboardComponent
  ],
  templateUrl: './dashboard.component.html',

  styleUrls: ['./dashboard.component.scss']

})
export class DashboardComponent implements OnInit {
  stats = {
    totalScans: 0,
    criticalIssues: 0,
    highIssues: 0,
    mediumIssues: 0
  };

  recentScans: ScanResult[] = [];
  topIssues: SecurityIssue[] = [];
  displayedColumns: string[] = ['id', 'project', 'chains', 'status', 'issues', 'date', 'actions'];

  // Additional properties for enhanced header
  get totalScans(): number { return this.stats.totalScans; }
  get activeScans(): number { return this.recentScans.filter(scan => scan.status === 'running').length; }
  get criticalIssues(): number { return this.stats.criticalIssues; }

  constructor(private apiService: ApiService) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    // Load recent scans
    this.apiService.getScanHistory().subscribe({
      next: (response) => {
        this.recentScans = response.scans.slice(0, 5); // Show only 5 most recent
        this.calculateStats(response.scans);
      },
      error: () => {
        this.recentScans = [];
        this.stats = {
          totalScans: 0,
          criticalIssues: 0,
          highIssues: 0,
          mediumIssues: 0
        };
      }
    });
  }



  calculateStats(scans: ScanResult[]): void {
    this.stats.totalScans = scans.length;
    
    let criticalTotal = 0;
    let highTotal = 0;
    let mediumTotal = 0;
    let allIssues: SecurityIssue[] = [];

    scans.forEach(scan => {
      criticalTotal += scan.severity_counts?.['critical'] || 0;
      highTotal += scan.severity_counts?.['high'] || 0;
      mediumTotal += scan.severity_counts?.['medium'] || 0;
      allIssues = allIssues.concat(scan.issues || []);
    });

    this.stats.criticalIssues = criticalTotal;
    this.stats.highIssues = highTotal;
    this.stats.mediumIssues = mediumTotal;

    // Get top 5 most severe issues
    this.topIssues = allIssues
      .sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity))
      .slice(0, 5);
  }

  getSeverityWeight(severity: string): number {
    const weights = { critical: 4, high: 3, medium: 2, low: 1, info: 0 };
    return weights[severity as keyof typeof weights] || 0;
  }

  getSeverityColor(severity: string): string {
    return SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || '#666';
  }

  getProjectName(path: string): string {
    return path.split('/').pop() || path;
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  formatTime(dateString: string): string {
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  getStatusIcon(status: string): string {
    const icons = {
      completed: 'check_circle',
      running: 'hourglass_empty',
      failed: 'error',
      pending: 'schedule'
    };
    return icons[status as keyof typeof icons] || 'help';
  }

  getIssueCount(scan: ScanResult, severity: string): number {
    return scan.severity_counts?.[severity] || 0;
  }

  // Chart data methods
  getSecurityMetricsData(): SecurityMetric[] {
    return [
      {
        label: 'Critical',
        value: this.stats.criticalIssues,
        severity: 'critical',
        trend: 15 // Mock trend data
      },
      {
        label: 'High',
        value: this.stats.highIssues,
        severity: 'high',
        trend: -5
      },
      {
        label: 'Medium',
        value: this.stats.mediumIssues,
        severity: 'medium',
        trend: 8
      },
      {
        label: 'Low',
        value: Math.max(0, this.stats.totalScans * 2 - this.stats.criticalIssues - this.stats.highIssues - this.stats.mediumIssues),
        severity: 'low',
        trend: -12
      }
    ];
  }

  getActiveScan(): ScanProgress | null {
    const activeScan = this.recentScans.find(scan => scan.status === 'running');
    if (!activeScan) return null;

    return {
      scanId: activeScan.id,
      projectName: this.getProjectName(activeScan.project_path),
      status: 'running',
      progress: 65, // Mock progress
      currentStep: 'Analyzing smart contracts',
      totalSteps: 6,
      completedSteps: 3,
      startTime: new Date(activeScan.created_at),
      estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes from now
      filesScanned: 45,
      totalFiles: 78,
      issuesFound: activeScan.issues?.length || 0
    };
  }

  getSecurityTrendsData(): SecurityTrend | null {
    // TODO: Implement real trend data from API
    return null;
  }
}
