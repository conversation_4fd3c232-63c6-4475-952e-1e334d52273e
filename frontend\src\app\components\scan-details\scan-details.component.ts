import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { ApiService } from '../../services/api.service';
import { ScanResult, SecurityIssue, SEVERITY_COLORS } from '../../models/security.models';

@Component({
  selector: 'app-scan-details',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatChipsModule,
    MatIconModule,
    MatButtonModule,
    MatTableModule
  ],
  template: `
    <div class="scan-details-container" *ngIf="scanResult">
      <h1>📊 Scan Results: {{ scanResult.id }}</h1>
      
      <!-- Scan Summary -->
      <mat-card class="summary-card">
        <mat-card-header>
          <mat-card-title>Scan Summary</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="summary-grid">
            <div class="summary-item">
              <strong>Project:</strong> {{ getProjectName(scanResult.project_path) }}
            </div>
            <div class="summary-item">
              <strong>Status:</strong> 
              <mat-chip [class]="'status-' + scanResult.status">{{ scanResult.status }}</mat-chip>
            </div>
            <div class="summary-item">
              <strong>Duration:</strong> {{ formatDuration(scanResult.duration) }}
            </div>
            <div class="summary-item">
              <strong>Files Scanned:</strong> {{ scanResult.files_scanned }}
            </div>
            <div class="summary-item">
              <strong>Chains:</strong>
              <mat-chip-listbox>
                <mat-chip *ngFor="let chain of scanResult.chains">{{ chain }}</mat-chip>
              </mat-chip-listbox>
            </div>
            <div class="summary-item">
              <strong>Total Issues:</strong> {{ scanResult.issues.length || 0 }}
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Severity Breakdown -->
      <mat-card class="severity-card">
        <mat-card-header>
          <mat-card-title>Issues by Severity</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="severity-grid">
            <div *ngFor="let severity of severityTypes" class="severity-item">
              <div class="severity-count" [style.color]="getSeverityColor(severity)">
                {{ scanResult.severity_counts[severity] || 0 }}
              </div>
              <div class="severity-label">{{ severity | titlecase }}</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Issues Table -->
      <mat-card class="issues-card">
        <mat-card-header>
          <mat-card-title>Security Issues</mat-card-title>
          <button mat-button color="primary" (click)="exportResults()">
            <mat-icon>download</mat-icon>
            Export
          </button>
        </mat-card-header>
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="scanResult.issues || []" class="issues-table">
              <ng-container matColumnDef="severity">
                <th mat-header-cell *matHeaderCellDef>Severity</th>
                <td mat-cell *matCellDef="let issue">
                  <mat-chip [style.background-color]="getSeverityColor(issue.severity)">
                    {{ issue.severity }}
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="title">
                <th mat-header-cell *matHeaderCellDef>Issue</th>
                <td mat-cell *matCellDef="let issue">
                  <div class="issue-title">{{ issue.title }}</div>
                  <div class="issue-description">{{ issue.description }}</div>
                </td>
              </ng-container>

              <ng-container matColumnDef="location">
                <th mat-header-cell *matHeaderCellDef>Location</th>
                <td mat-cell *matCellDef="let issue">
                  <div class="file-location">{{ issue.file }}:{{ issue.line }}</div>
                </td>
              </ng-container>

              <ng-container matColumnDef="chain">
                <th mat-header-cell *matHeaderCellDef>Chain</th>
                <td mat-cell *matCellDef="let issue">
                  <mat-chip>{{ issue.chain }}</mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="category">
                <th mat-header-cell *matHeaderCellDef>Category</th>
                <td mat-cell *matCellDef="let issue">{{ issue.category }}</td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
                  (click)="selectIssue(row)" 
                  [class.selected]="selectedIssue?.id === row.id"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Issue Details -->
      <mat-card *ngIf="selectedIssue" class="issue-details-card">
        <mat-card-header>
          <mat-card-title>Issue Details</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="issue-detail">
            <h3>{{ selectedIssue.title }}</h3>
            <p><strong>Description:</strong> {{ selectedIssue.description }}</p>
            <p><strong>File:</strong> {{ selectedIssue.file }}:{{ selectedIssue.line }}</p>
            <p><strong>Code:</strong></p>
            <pre class="code-snippet">{{ selectedIssue.code }}</pre>
            <p *ngIf="selectedIssue.suggestion"><strong>Suggestion:</strong> {{ selectedIssue.suggestion }}</p>
            <div *ngIf="selectedIssue.references?.length" class="references">
              <strong>References:</strong>
              <ul>
                <li *ngFor="let ref of selectedIssue.references">
                  <a [href]="ref" target="_blank">{{ ref }}</a>
                </li>
              </ul>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <div *ngIf="!scanResult" class="loading-container">
      <p>Loading scan results...</p>
    </div>
  `,
  styles: [`
    .scan-details-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    h1 {
      margin-bottom: 30px;
      color: #333;
    }

    .summary-card,
    .severity-card,
    .issues-card,
    .issue-details-card {
      margin-bottom: 30px;
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
    }

    .summary-item {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .severity-grid {
      display: flex;
      justify-content: space-around;
      text-align: center;
    }

    .severity-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
    }

    .severity-count {
      font-size: 48px;
      font-weight: bold;
    }

    .severity-label {
      font-size: 14px;
      color: #666;
      text-transform: uppercase;
    }

    .table-container {
      overflow-x: auto;
    }

    .issues-table {
      width: 100%;
    }

    .issues-table tr {
      cursor: pointer;
    }

    .issues-table tr:hover {
      background-color: #f5f5f5;
    }

    .issues-table tr.selected {
      background-color: #e3f2fd;
    }

    .issue-title {
      font-weight: bold;
      margin-bottom: 5px;
    }

    .issue-description {
      font-size: 14px;
      color: #666;
    }

    .file-location {
      font-family: monospace;
      font-size: 12px;
      color: #333;
    }

    .status-completed {
      background-color: #4caf50;
      color: white;
    }

    .status-running {
      background-color: #2196f3;
      color: white;
    }

    .status-failed {
      background-color: #f44336;
      color: white;
    }

    .status-pending {
      background-color: #ff9800;
      color: white;
    }

    .issue-detail h3 {
      margin-top: 0;
      color: #333;
    }

    .code-snippet {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      font-family: monospace;
      font-size: 14px;
    }

    .references ul {
      margin: 10px 0;
      padding-left: 20px;
    }

    .references a {
      color: #1976d2;
      text-decoration: none;
    }

    .references a:hover {
      text-decoration: underline;
    }

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      color: #666;
    }
  `]
})
export class ScanDetailsComponent implements OnInit {
  scanResult: ScanResult | null = null;
  selectedIssue: SecurityIssue | null = null;
  severityTypes = ['critical', 'high', 'medium', 'low', 'info'];
  displayedColumns = ['severity', 'title', 'location', 'chain', 'category'];

  constructor(
    private route: ActivatedRoute,
    private apiService: ApiService
  ) {}

  ngOnInit(): void {
    const scanId = this.route.snapshot.paramMap.get('id');
    if (scanId) {
      this.loadScanResult(scanId);
    }
  }

  loadScanResult(scanId: string): void {
    this.apiService.getScanResult(scanId).subscribe({
      next: (result) => {
        this.scanResult = result;
      },
      error: () => {
        this.scanResult = null;
      }
    });
  }

  selectIssue(issue: SecurityIssue): void {
    this.selectedIssue = issue;
  }

  getSeverityColor(severity: string): string {
    return SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || '#666';
  }

  getProjectName(path: string): string {
    return path.split('/').pop() || path;
  }

  formatDuration(duration: number): string {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }

  exportResults(): void {
    if (this.scanResult) {
      const dataStr = JSON.stringify(this.scanResult, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `scan-results-${this.scanResult.id}.json`;
      link.click();
      URL.revokeObjectURL(url);
    }
  }
}
