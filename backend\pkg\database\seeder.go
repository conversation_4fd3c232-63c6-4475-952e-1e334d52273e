package database

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"os"

	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/bcrypt"
)

// SeedDefaultUser creates a default admin user if no users exist
func (d *Database) SeedDefaultUser() error {
	if d.db == nil {
		d.logger.Warn("Database not connected, skipping user seeding")
		return nil
	}

	// Check if any users exist
	users, err := d.GetAllUsers()
	if err != nil {
		return fmt.Errorf("failed to check existing users: %w", err)
	}

	if len(users) > 0 {
		d.logger.Info("Users already exist, skipping default user creation")
		return nil
	}

	// Create default admin user
	defaultUser := &models.User{
		Username:  getEnvOrDefault("SPT_DEFAULT_USERNAME", "admin"),
		Email:     getEnvOrDefault("SPT_DEFAULT_EMAIL", "<EMAIL>"),
		FirstName: stringPtr("SPT"),
		LastName:  stringPtr("Administrator"),
		Role:      models.UserRoleAdmin,
		IsActive:  true,
	}

	// Set password
	defaultPassword := getEnvOrDefault("SPT_DEFAULT_PASSWORD", "spt123456")
	if err := setUserPassword(defaultUser, defaultPassword); err != nil {
		return fmt.Errorf("failed to set user password: %w", err)
	}

	// Create the user
	if err := d.CreateUser(defaultUser); err != nil {
		return fmt.Errorf("failed to create default user: %w", err)
	}

	d.logger.WithFields(logrus.Fields{
		"username": defaultUser.Username,
		"email":    defaultUser.Email,
		"role":     defaultUser.Role,
	}).Info("Default admin user created successfully")

	// Log credentials for first-time setup
	fmt.Println()
	fmt.Println("🔐 Default Admin User Created")
	fmt.Println("============================")
	fmt.Printf("Username: %s\n", defaultUser.Username)
	fmt.Printf("Email:    %s\n", defaultUser.Email)
	fmt.Printf("Password: %s\n", defaultPassword)
	fmt.Printf("Role:     %s\n", defaultUser.Role)
	fmt.Println()
	fmt.Println("⚠️  IMPORTANT: Please change the default password after first login!")
	fmt.Println("============================")
	fmt.Println()

	return nil
}

// SeedDevelopmentUsers creates additional users for development environment
func (d *Database) SeedDevelopmentUsers() error {
	if d.db == nil {
		d.logger.Warn("Database not connected, skipping development user seeding")
		return nil
	}

	// Only seed in development environment
	env := getEnvOrDefault("SPT_ENVIRONMENT", "development")
	if env != "development" {
		d.logger.Info("Not in development environment, skipping development user seeding")
		return nil
	}

	developmentUsers := []*models.User{
		{
			Username:  "developer",
			Email:     "<EMAIL>",
			FirstName: stringPtr("John"),
			LastName:  stringPtr("Developer"),
			Role:      models.UserRoleDeveloper,
			IsActive:  true,
		},
		{
			Username:  "viewer",
			Email:     "<EMAIL>",
			FirstName: stringPtr("Jane"),
			LastName:  stringPtr("Viewer"),
			Role:      models.UserRoleViewer,
			IsActive:  true,
		},
	}

	for _, user := range developmentUsers {
		// Check if user already exists
		exists, err := d.UserExists(user.Username)
		if err != nil {
			d.logger.WithError(err).Warnf("Failed to check if user %s exists", user.Username)
			continue
		}

		if exists {
			d.logger.Infof("User %s already exists, skipping", user.Username)
			continue
		}

		// Set default password
		if err := setUserPassword(user, "password123"); err != nil {
			d.logger.WithError(err).Warnf("Failed to set password for user %s", user.Username)
			continue
		}

		// Create the user
		if err := d.CreateUser(user); err != nil {
			d.logger.WithError(err).Warnf("Failed to create user %s", user.Username)
			continue
		}

		d.logger.WithFields(logrus.Fields{
			"username": user.Username,
			"email":    user.Email,
			"role":     user.Role,
		}).Info("Development user created successfully")
	}

	return nil
}

// SeedAll runs all seeding operations
func (d *Database) SeedAll() error {
	d.logger.Info("Starting database seeding...")

	// Seed default admin user
	if err := d.SeedDefaultUser(); err != nil {
		return fmt.Errorf("failed to seed default user: %w", err)
	}

	// Seed development users if in development environment
	if err := d.SeedDevelopmentUsers(); err != nil {
		return fmt.Errorf("failed to seed development users: %w", err)
	}

	// Seed API keys for testing
	if err := d.SeedAPIKeys(); err != nil {
		return fmt.Errorf("failed to seed API keys: %w", err)
	}

	d.logger.Info("Database seeding completed successfully")
	return nil
}

// SeedAPIKeys creates default API keys for testing
func (d *Database) SeedAPIKeys() error {
	if d.db == nil {
		d.logger.Warn("Database not connected, skipping API key seeding")
		return nil
	}

	// Get admin user
	adminUser, err := d.GetUserByUsername("admin")
	if err != nil {
		d.logger.WithError(err).Warn("Admin user not found, skipping API key seeding")
		return nil // Don't fail if admin user doesn't exist
	}

	// Check if VS Code extension API key already exists
	vsCodeKeyHash := sha256.Sum256([]byte("vscode-extension-key"))
	vsCodeKeyHashStr := hex.EncodeToString(vsCodeKeyHash[:])

	var count int64
	d.db.Model(&models.APIKey{}).Where("key_hash = ?", vsCodeKeyHashStr).Count(&count)
	if count > 0 {
		d.logger.Info("API keys already exist, skipping API key seeding")
		return nil
	}

	// Create API keys for testing
	apiKeys := []*models.APIKey{
		{
			UserID:      adminUser.ID,
			Name:        "VS Code Extension",
			KeyHash:     vsCodeKeyHashStr,
			Prefix:      "vscode-ext",
			Permissions: []string{"scan:read", "scan:write", "report:read", "report:write"},
			IsActive:    true,
		},
		{
			UserID:      adminUser.ID,
			Name:        "Development Key",
			KeyHash:     func() string { h := sha256.Sum256([]byte("dev-key-123")); return hex.EncodeToString(h[:]) }(),
			Prefix:      "dev-key-12",
			Permissions: []string{"scan:read", "scan:write"},
			IsActive:    true,
		},
	}

	for _, apiKey := range apiKeys {
		if err := d.CreateAPIKey(apiKey); err != nil {
			d.logger.WithError(err).Warnf("Failed to create API key: %s", apiKey.Name)
		} else {
			d.logger.WithFields(logrus.Fields{
				"name":   apiKey.Name,
				"prefix": apiKey.Prefix,
			}).Info("API key created successfully")
		}
	}

	// Log the test API keys for development
	env := getEnvOrDefault("SPT_ENVIRONMENT", "development")
	if env == "development" {
		fmt.Println()
		fmt.Println("🔑 Test API Keys Created")
		fmt.Println("========================")
		fmt.Println("VS Code Extension Key: vscode-extension-key")
		fmt.Println("Development Key:       dev-key-123")
		fmt.Println()
		fmt.Println("⚠️  These are test keys for development only!")
		fmt.Println("========================")
		fmt.Println()
	}

	return nil
}

// setUserPassword hashes and sets the user password
func setUserPassword(user *models.User, password string) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	user.Password = string(hashedPassword)
	return nil
}

// Helper functions
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func stringPtr(s string) *string {
	return &s
}
