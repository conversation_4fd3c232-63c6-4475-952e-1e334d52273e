package api

import (
	"fmt"
	"net/http"
	"net/url"
	"time"

	"blockchain-spt/backend/pkg/models"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

// LoginRequest represents a login request
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterRequest represents a registration request
type RegisterRequest struct {
	Username  string `json:"username" binding:"required"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=6"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
}

// AuthResponse represents an authentication response
type AuthResponse struct {
	Token        string      `json:"token"`
	RefreshToken string      `json:"refresh_token"`
	User         models.User `json:"user"`
	ExpiresIn    int64       `json:"expires_in"`
}

// RefreshRequest represents a token refresh request
type RefreshRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// UserStorage interface for user operations
type UserStorage interface {
	GetUserByUsername(username string) (*models.User, error)
	GetUserByEmail(email string) (*models.User, error)
	CreateUser(user *models.User) error
	UpdateUser(user *models.User) error
	UserExists(username string) (bool, error)
}

// Login handles user authentication
func (h *Handler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get user storage interface
	userStorage, ok := h.storage.(UserStorage)
	if !ok {
		h.logger.Error("Storage does not implement UserStorage interface")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Authentication service unavailable",
		})
		return
	}

	// Find user by username
	user, err := userStorage.GetUserByUsername(req.Username)
	if err != nil {
		h.logger.WithError(err).Warnf("Failed to find user: %s", req.Username)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	// Check if user is active
	if !user.IsActive {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Account is disabled",
		})
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		h.logger.WithError(err).Warnf("Invalid password for user: %s", req.Username)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	// Generate authentication token
	token, err := h.generateAuthToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate auth token")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate authentication token",
		})
		return
	}

	// Generate refresh token (for now, same as access token but with longer expiration)
	refreshToken, err := h.generateRefreshToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate refresh token")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate refresh token",
		})
		return
	}

	// Update last login time
	now := time.Now()
	user.LastLogin = &now
	if err := userStorage.UpdateUser(user); err != nil {
		h.logger.WithError(err).Warn("Failed to update last login time")
	}

	// Remove password from response
	user.Password = ""

	c.JSON(http.StatusOK, AuthResponse{
		Token:        token,
		RefreshToken: refreshToken,
		User:         *user,
		ExpiresIn:    86400, // 24 hours
	})
}

// Register handles user registration
func (h *Handler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get user storage interface
	userStorage, ok := h.storage.(UserStorage)
	if !ok {
		h.logger.Error("Storage does not implement UserStorage interface")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration service unavailable",
		})
		return
	}

	// Check if username already exists
	exists, err := userStorage.UserExists(req.Username)
	if err != nil {
		h.logger.WithError(err).Error("Failed to check if user exists")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration failed",
		})
		return
	}

	if exists {
		c.JSON(http.StatusConflict, gin.H{
			"error": "Username already exists",
		})
		return
	}

	// Check if email already exists
	if _, err := userStorage.GetUserByEmail(req.Email); err == nil {
		c.JSON(http.StatusConflict, gin.H{
			"error": "Email already registered",
		})
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		h.logger.WithError(err).Error("Failed to hash password")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration failed",
		})
		return
	}

	// Create new user
	user := &models.User{
		Username:  req.Username,
		Email:     req.Email,
		Password:  string(hashedPassword),
		FirstName: stringPtr(req.FirstName),
		LastName:  stringPtr(req.LastName),
		Role:      models.UserRoleDeveloper, // Default role
		IsActive:  true,
	}

	if err := userStorage.CreateUser(user); err != nil {
		h.logger.WithError(err).Error("Failed to create user")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration failed",
		})
		return
	}

	// Generate authentication token
	token, err := h.generateAuthToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate auth token")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration successful but failed to generate token",
		})
		return
	}

	// Generate refresh token
	refreshToken, err := h.generateRefreshToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate refresh token")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Registration successful but failed to generate refresh token",
		})
		return
	}

	// Remove password from response
	user.Password = ""

	c.JSON(http.StatusCreated, AuthResponse{
		Token:        token,
		RefreshToken: refreshToken,
		User:         *user,
		ExpiresIn:    86400, // 24 hours
	})
}

// Logout handles user logout
func (h *Handler) Logout(c *gin.Context) {
	// In a real implementation, you would invalidate the token
	// For now, we'll just return success
	c.JSON(http.StatusOK, gin.H{
		"message": "Logged out successfully",
	})
}

// RefreshToken handles token refresh
func (h *Handler) RefreshToken(c *gin.Context) {
	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Validate the refresh token (for now, we'll use the same JWT validation)
	// In a production system, refresh tokens should be stored separately and have longer expiration
	claims, err := validateJWTToken(req.RefreshToken)
	if err != nil {
		h.logger.WithError(err).Warn("Invalid refresh token")
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid or expired refresh token",
		})
		return
	}

	// Get user storage from context
	userStorage, exists := c.Get("userStorage")
	if !exists {
		h.logger.Error("User storage not found in context")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Internal server error",
		})
		return
	}

	// Get user from database to ensure they still exist and are active
	user, err := userStorage.(UserStorage).GetUserByUsername(claims.Username)
	if err != nil {
		h.logger.WithError(err).Warnf("Failed to find user during refresh: %s", claims.Username)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found",
		})
		return
	}

	// Check if user is still active
	if !user.IsActive {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Account is disabled",
		})
		return
	}

	// Generate new access token
	newToken, err := h.generateAuthToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate new auth token")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate new authentication token",
		})
		return
	}

	// Generate new refresh token
	newRefreshToken, err := h.generateRefreshToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate new refresh token")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate new refresh token",
		})
		return
	}

	// Remove password from response
	user.Password = ""

	c.JSON(http.StatusOK, AuthResponse{
		Token:        newToken,
		RefreshToken: newRefreshToken,
		User:         *user,
		ExpiresIn:    86400, // 24 hours
	})
}

// GetCurrentUser returns the current authenticated user
func (h *Handler) GetCurrentUser(c *gin.Context) {
	// Get user from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Not authenticated",
		})
		return
	}

	// For now, return a mock user based on the user_id
	// In a real implementation, fetch from database
	c.JSON(http.StatusOK, gin.H{
		"user": gin.H{
			"id":       userID,
			"username": "current_user",
			"role":     "developer",
		},
	})
}

// generateAuthToken generates a JWT authentication token
func (h *Handler) generateAuthToken(user *models.User) (string, error) {
	// Create JWT claims
	claims := JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)), // 24 hours
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "spt-backend",
			Subject:   user.ID,
		},
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token with secret
	tokenString, err := token.SignedString(jwtSecret)
	if err != nil {
		return "", fmt.Errorf("failed to sign JWT token: %w", err)
	}

	return tokenString, nil
}

// generateRefreshToken generates a JWT refresh token with longer expiration
func (h *Handler) generateRefreshToken(user *models.User) (string, error) {
	// Create JWT claims for refresh token
	claims := JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(7 * 24 * time.Hour)), // 7 days
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "spt-backend",
			Subject:   user.ID,
		},
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token with secret
	tokenString, err := token.SignedString(jwtSecret)
	if err != nil {
		return "", fmt.Errorf("failed to sign JWT refresh token: %w", err)
	}

	return tokenString, nil
}

// VSCodeAuth handles VS Code extension authentication redirect
func (h *Handler) VSCodeAuth(c *gin.Context) {
	state := c.Query("state")
	redirectURI := c.Query("redirect_uri")

	if state == "" || redirectURI == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing required parameters",
		})
		return
	}

	// Redirect to frontend login page with VS Code parameters
	frontendURL := fmt.Sprintf("%s/auth/login", h.config.Server.FrontendURL)
	redirectURL := fmt.Sprintf("%s?vscode=true&state=%s&redirect_uri=%s",
		frontendURL,
		url.QueryEscape(state),
		url.QueryEscape(redirectURI))

	c.Redirect(http.StatusFound, redirectURL)
}

// VSCodeAuthCallback handles the authentication callback for VS Code
func (h *Handler) VSCodeAuthCallback(c *gin.Context) {
	var req struct {
		State    string `json:"state" binding:"required"`
		Username string `json:"username" binding:"required"`
		Password string `json:"password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get user storage interface
	userStorage, ok := h.storage.(UserStorage)
	if !ok {
		h.logger.Error("Storage does not implement UserStorage interface")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Authentication service unavailable",
		})
		return
	}

	// Find user by username
	user, err := userStorage.GetUserByUsername(req.Username)
	if err != nil {
		h.logger.WithError(err).Warnf("Failed to find user: %s", req.Username)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	// Check if user is active
	if !user.IsActive {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Account is disabled",
		})
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		h.logger.WithError(err).Warnf("Invalid password for user: %s", req.Username)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid credentials",
		})
		return
	}

	// Generate authentication token
	token, err := h.generateAuthToken(user)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate auth token")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate authentication token",
		})
		return
	}

	// Remove password from response
	user.Password = ""

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"token":      token,
		"user":       user,
		"expires_in": 86400, // 24 hours
		"state":      req.State,
	})
}

// Helper function
func stringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}
