package api

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/sirupsen/logrus"
)

// AuthMiddleware provides JWT-only authentication middleware
func AuthMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// Skip authentication for health check and public endpoints
		if isPublicEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Check for JWT token in Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			logrus.Warn("No authorization header provided")
			c.<PERSON><PERSON><PERSON>(http.StatusUnauthorized, gin.H{
				"error":   "Authorization header required",
				"message": "Please authenticate to access this resource",
			})
			c.Abort()
			return
		}

		if !strings.HasPrefix(authHeader, "Bearer ") {
			logrus.Warn("Invalid authorization header format")
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"error":   "Invalid authorization format",
				"message": "Bearer token required",
			})
			c.Abort()
			return
		}

		token := strings.TrimPrefix(authHeader, "Bearer ")

		// Validate token is not empty
		if token == "" {
			logrus.Warn("Empty bearer token provided")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Empty token",
				"message": "Please provide a valid JWT token",
			})
			c.Abort()
			return
		}

		// Validate JWT token
		claims, err := validateJWTToken(token)
		if err != nil {
			logrus.WithError(err).Warn("Invalid JWT token provided")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid or expired JWT token",
				"message": "Please re-authenticate",
			})
			c.Abort()
			return
		}

		// Set user context from JWT claims
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("auth_type", "jwt")
		c.Set("token", token)
		c.Set("jwt_claims", claims)

		logrus.Infof("JWT authentication successful for user: %s", claims.Username)

		c.Next()
	})
}

// RateLimitMiddleware provides rate limiting
func RateLimitMiddleware() gin.HandlerFunc {
	// Simple in-memory rate limiter
	// In production, use Redis or similar
	requests := make(map[string][]time.Time)

	return gin.HandlerFunc(func(c *gin.Context) {
		clientIP := c.ClientIP()
		now := time.Now()

		// Clean old requests (older than 1 minute)
		if times, exists := requests[clientIP]; exists {
			var validTimes []time.Time
			for _, t := range times {
				if now.Sub(t) < time.Minute {
					validTimes = append(validTimes, t)
				}
			}
			requests[clientIP] = validTimes
		}

		// Check rate limit (100 requests per minute)
		if len(requests[clientIP]) >= 100 {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "Rate limit exceeded",
				"code":        "RATE_LIMIT_EXCEEDED",
				"retry_after": 60,
			})
			c.Abort()
			return
		}

		// Add current request
		requests[clientIP] = append(requests[clientIP], now)

		c.Next()
	})
}

// CORSMiddleware provides CORS support
func CORSMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// Allow specific origins in production
		allowedOrigins := []string{
			"http://localhost:4200",
			"http://localhost:3000",
			"http://127.0.0.1:4200",
			"http://127.0.0.1:3000",
		}

		isAllowed := false
		for _, allowed := range allowedOrigins {
			if origin == allowed {
				isAllowed = true
				break
			}
		}

		if isAllowed || origin == "" {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization, X-API-Key, X-Requested-With")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	})
}

// LoggingMiddleware provides structured logging
func LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logrus.WithFields(logrus.Fields{
			"timestamp":  param.TimeStamp.Format(time.RFC3339),
			"status":     param.StatusCode,
			"latency":    param.Latency,
			"client_ip":  param.ClientIP,
			"method":     param.Method,
			"path":       param.Path,
			"user_agent": param.Request.UserAgent(),
			"error":      param.ErrorMessage,
		}).Info("API Request")

		return ""
	})
}

// SecurityMiddleware adds security headers
func SecurityMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// Security headers
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")

		c.Next()
	})
}

// ErrorHandlingMiddleware provides centralized error handling
func ErrorHandlingMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		c.Next()

		// Handle any errors that occurred during request processing
		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			logrus.WithFields(logrus.Fields{
				"path":   c.Request.URL.Path,
				"method": c.Request.Method,
				"error":  err.Error(),
			}).Error("Request error")

			// Don't override status if already set
			if c.Writer.Status() == http.StatusOK {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "Internal server error",
					"code":  "INTERNAL_ERROR",
				})
			}
		}
	})
}

// Helper functions
func isPublicEndpoint(path string) bool {
	publicEndpoints := []string{
		"/health",
		"/api/v1/health",
		"/api/v1/auth/login",
		"/api/v1/auth/register",
		"/api/v1/auth/refresh",
		"/metrics",
		"/docs",
		"/swagger",
		"/ws", // WebSocket endpoint
	}

	for _, endpoint := range publicEndpoints {
		if strings.HasPrefix(path, endpoint) {
			return true
		}
	}

	return false
}

// validateJWTToken validates a JWT token and returns the claims
func validateJWTToken(tokenString string) (*JWTClaims, error) {
	// Parse and validate the JWT token
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Verify the signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return jwtSecret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse JWT token: %w", err)
	}

	// Extract and validate claims
	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		// Additional validation can be added here
		if claims.UserID == "" || claims.Username == "" {
			return nil, fmt.Errorf("invalid token claims: missing required fields")
		}
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token claims")
}

// RequestIDMiddleware adds a unique request ID to each request
func RequestIDMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}

		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)

		c.Next()
	})
}

func generateRequestID() string {
	// Simple request ID generation
	// In production, use UUID or similar
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

// MetricsMiddleware collects basic metrics
func MetricsMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		start := time.Now()

		c.Next()

		duration := time.Since(start)

		// Log metrics (in production, send to metrics service)
		logrus.WithFields(logrus.Fields{
			"endpoint":      c.Request.URL.Path,
			"method":        c.Request.Method,
			"status_code":   c.Writer.Status(),
			"duration_ms":   duration.Milliseconds(),
			"request_size":  c.Request.ContentLength,
			"response_size": c.Writer.Size(),
		}).Debug("Request metrics")
	})
}
