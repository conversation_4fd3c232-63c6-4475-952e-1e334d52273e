package api

import (
	"os"

	"github.com/golang-jwt/jwt/v5"
)

// JWT secret key - loaded from environment variable or default for development
var jwtSecret = getJWTSecret()

// getJWTSecret returns the JWT secret from environment variable or a default for development
func getJWTSecret() []byte {
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		// Default secret for development - should be changed in production
		secret = "spt-development-jwt-secret-change-in-production-2024"
	}
	return []byte(secret)
}

// JWTClaims represents the JWT claims
type JWTClaims struct {
	UserID   string `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}
