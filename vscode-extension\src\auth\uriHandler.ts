import * as vscode from 'vscode';
import { AuthenticationManager } from './manager';

export class AuthUriHandler implements vscode.UriHandler {
    constructor(private authManager: AuthenticationManager) {}

    async handleUri(uri: vscode.Uri): Promise<void> {
        try {
            // Parse the URI
            const path = uri.path;
            const query = new URLSearchParams(uri.query);

            if (path === '/auth/callback') {
                await this.handleAuthCallback(query);
            } else {
                vscode.window.showWarningMessage(`Unknown URI path: ${path}`);
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to handle URI: ${error}`);
        }
    }

    private async handleAuthCallback(query: URLSearchParams): Promise<void> {
        const token = query.get('token');
        const state = query.get('state');
        const error = query.get('error');
        const userParam = query.get('user');
        const expiresIn = query.get('expires_in');

        // Handle error case
        if (error) {
            vscode.window.showErrorMessage(`Authentication failed: ${error}`);
            return;
        }

        // Validate required parameters
        if (!token || !state || !userParam || !expiresIn) {
            vscode.window.showErrorMessage('Invalid authentication callback parameters');
            return;
        }

        // Validate state parameter
        if (!(await this.authManager.validatePendingAuth(state))) {
            vscode.window.showErrorMessage('Invalid authentication state. Please try again.');
            return;
        }

        try {
            // Parse user data
            const user = JSON.parse(decodeURIComponent(userParam));
            const expiresInSeconds = parseInt(expiresIn, 10);

            // Complete authentication
            await this.authManager.handleAuthCallback(token, user, expiresInSeconds);
        } catch (parseError) {
            vscode.window.showErrorMessage(`Failed to parse authentication data: ${parseError}`);
        }
    }
}
