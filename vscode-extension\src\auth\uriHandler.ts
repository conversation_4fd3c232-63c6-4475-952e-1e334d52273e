import * as vscode from 'vscode';
import { AuthenticationManager } from './manager';

export class AuthUriHandler implements vscode.UriHandler {
    constructor(private authManager: AuthenticationManager) {}

    async handleUri(uri: vscode.Uri): Promise<void> {
        try {
            console.log('VS Code URI handler received:', uri.toString());

            // Parse the URI
            const path = uri.path;
            const query = new URLSearchParams(uri.query);

            console.log('URI path:', path);
            console.log('URI query params:', Array.from(query.entries()));

            if (path === '/auth/callback') {
                await this.handleAuthCallback(query);
            } else {
                vscode.window.showWarningMessage(`Unknown URI path: ${path}`);
            }
        } catch (error) {
            console.error('Failed to handle URI:', error);
            vscode.window.showErrorMessage(`Failed to handle URI: ${error}`);
        }
    }

    private async handleAuthCallback(query: URLSearchParams): Promise<void> {
        console.log('Handling auth callback with query params:', Array.from(query.entries()));

        const token = query.get('token');
        const state = query.get('state');
        const error = query.get('error');
        const userParam = query.get('user');
        const expiresIn = query.get('expires_in');

        console.log('Extracted parameters:', { token: token ? 'present' : 'missing', state, error, userParam: userParam ? 'present' : 'missing', expiresIn });

        // Handle error case
        if (error) {
            console.error('Authentication error:', error);
            vscode.window.showErrorMessage(`Authentication failed: ${error}`);
            return;
        }

        // Validate required parameters
        if (!token || !state || !userParam || !expiresIn) {
            console.error('Missing required parameters:', { token: !!token, state: !!state, userParam: !!userParam, expiresIn: !!expiresIn });
            vscode.window.showErrorMessage('Invalid authentication callback parameters');
            return;
        }

        // Validate state parameter
        console.log('Validating state parameter...');
        if (!(await this.authManager.validatePendingAuth(state))) {
            console.error('Invalid state parameter');
            vscode.window.showErrorMessage('Invalid authentication state. Please try again.');
            return;
        }

        try {
            // Parse user data
            console.log('Parsing user data...');
            const user = JSON.parse(decodeURIComponent(userParam));
            const expiresInSeconds = parseInt(expiresIn, 10);

            console.log('Parsed user:', user);
            console.log('Expires in seconds:', expiresInSeconds);

            // Complete authentication
            console.log('Completing authentication...');
            await this.authManager.handleAuthCallback(token, user, expiresInSeconds);
            console.log('Authentication completed successfully');
        } catch (parseError) {
            console.error('Failed to parse authentication data:', parseError);
            vscode.window.showErrorMessage(`Failed to parse authentication data: ${parseError}`);
        }
    }
}
