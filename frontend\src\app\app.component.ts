import { Component, OnInit } from '@angular/core';
import { CommonModule, TitleCasePipe } from '@angular/common';
import { RouterOutlet, RouterModule, Router, NavigationEnd } from '@angular/router';
import { Observable } from 'rxjs';
import { filter, map, startWith } from 'rxjs/operators';
import { AuthService, User } from './services/auth.service';
import { NavigationComponent } from './shared/navigation/navigation.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    RouterModule,
    NavigationComponent
  ],
  templateUrl: './app.component.html',


  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = 'SPT - Blockchain Security Protocol Tool';
  currentUser$: Observable<User | null>;
  isDocumentationRoute$: Observable<boolean>;

  constructor(
    private authService: AuthService,
    private router: Router
  ) {
    this.currentUser$ = this.authService.currentUser$;

    // Check if current route is documentation - include initial route
    this.isDocumentationRoute$ = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map((event: NavigationEnd) => event.url.startsWith('/doc')),
      // Start with current URL check
      startWith(this.router.url.startsWith('/doc'))
    );
  }

  ngOnInit(): void {
    // Handle initial routing based on authentication state
    this.handleInitialRouting();
  }

  private handleInitialRouting(): void {
    // Check if user is authenticated and handle routing
    const currentUrl = this.router.url;
    const isAuthPage = currentUrl === '/login' || currentUrl === '/register';

    if (this.authService.isAuthenticated() && isAuthPage) {
      // User is logged in but on auth page, redirect to dashboard
      this.router.navigate(['/dashboard']);
    }
  }
}
