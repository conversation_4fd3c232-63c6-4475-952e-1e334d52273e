# Blockchain Security Protocol (SPT) - VSCode Extension

🛡️ **Real-time blockchain security analysis for Ethereum, Bitcoin, and smart contracts**

The SPT VSCode Extension provides comprehensive security analysis for blockchain projects directly in your development environment. Get instant feedback on security vulnerabilities, smart contract issues, and best practices violations as you code.

## Features

### 🔍 **Real-time Security Analysis**
- **Live Code Scanning**: Automatic security analysis as you type
- **Multi-Chain Support**: Ethereum, Bitcoin, and general blockchain security
- **Smart Contract Auditing**: Solidity, Vyper, and other smart contract languages
- **Dependency Scanning**: Check for vulnerable packages and libraries

### 🚨 **Vulnerability Detection**
- **Critical Issue Alerts**: Immediate warnings for severe security flaws
- **Inline Decorations**: Visual indicators directly in your code
- **Detailed Explanations**: Comprehensive descriptions and fix suggestions
- **CWE/OWASP Mapping**: Industry-standard vulnerability classifications

### 🛠️ **Developer Productivity**
- **Code Completion**: Security-aware code suggestions
- **Quick Fixes**: Automated solutions for common security issues
- **Security Snippets**: Pre-built secure code templates
- **Hover Information**: Instant security insights on hover

### 📊 **Reporting & Analytics**
- **Security Dashboard**: Comprehensive project security overview
- **Detailed Reports**: Exportable security analysis reports
- **Progress Tracking**: Monitor security improvements over time
- **Team Collaboration**: Share security findings with your team

## Installation

1. **Install from VSCode Marketplace**:
   - Open VSCode
   - Go to Extensions (Ctrl+Shift+X)
   - Search for "Blockchain Security Protocol"
   - Click Install

2. **Install from VSIX**:
   ```bash
   code --install-extension blockchain-security-protocol-1.0.0.vsix
   ```

## Quick Start

### 1. Configure SPT Backend
First, ensure you have the SPT backend server running:

```bash
# Clone the SPT repository
git clone https://github.com/blockchain-spt/spt
cd spt

# Start the backend server
go run backend/cmd/main.go
```

### 2. Configure Extension Settings
Open VSCode settings and configure SPT:

```json
{
  "spt.serverUrl": "http://localhost:8080",
  "spt.apiKey": "your-api-key-here",
  "spt.autoScan": true,
  "spt.chains": ["ethereum", "bitcoin", "general"]
}
```

### 3. Start Scanning
- **Scan Project**: `Ctrl+Shift+P` → "SPT: Scan Project for Security Issues"
- **Scan File**: `Ctrl+Shift+P` → "SPT: Scan Current File"
- **Auto-scan**: Files are automatically scanned on save (if enabled)

## Configuration

### Extension Settings

| Setting | Description | Default |
|---------|-------------|---------|
| `spt.enabled` | Enable/disable SPT security analysis | `true` |
| `spt.serverUrl` | SPT backend server URL | `http://localhost:8080` |
| `spt.apiKey` | API key for authentication | `""` |
| `spt.autoScan` | Automatically scan files on save | `true` |
| `spt.scanOnOpen` | Automatically scan files when opened | `false` |
| `spt.chains` | Blockchain chains to analyze | `["ethereum", "bitcoin", "general"]` |
| `spt.severity` | Minimum severity level to show | `"medium"` |
| `spt.showInlineDecorations` | Show inline security decorations | `true` |
| `spt.showProblems` | Show security issues in Problems panel | `true` |
| `spt.enableCodeLens` | Enable security-related CodeLens | `true` |
| `spt.enableHover` | Enable security information on hover | `true` |

### Workspace Configuration
Create a `.vscode/spt.json` file in your workspace for project-specific settings:

```json
{
  "enabled": true,
  "chains": ["ethereum"],
  "severity": "high",
  "autoScan": true,
  "customRules": {
    "ethereum": {
      "reentrancy": true,
      "integerOverflow": true,
      "gasOptimization": true
    }
  }
}
```

## Commands

| Command | Description | Shortcut |
|---------|-------------|----------|
| `spt.scanProject` | Scan entire project for security issues | - |
| `spt.scanFile` | Scan current file | - |
| `spt.showSecurityReport` | Show detailed security report | - |
| `spt.openDashboard` | Open SPT web dashboard | - |
| `spt.configureSettings` | Open SPT settings | - |
| `spt.refreshSecurityView` | Refresh security issues view | - |
| `spt.fixIssue` | Apply automatic fix for security issue | - |
| `spt.ignoreIssue` | Ignore specific security issue | - |

## Supported Languages

### Primary Support
- **Solidity** (.sol) - Smart contracts for Ethereum
- **JavaScript** (.js) - Web3 applications and scripts
- **TypeScript** (.ts) - Type-safe blockchain applications
- **Python** (.py) - Blockchain scripts and applications
- **Go** (.go) - Blockchain backend services
- **Rust** (.rs) - High-performance blockchain applications

### Configuration Files
- **package.json** - Node.js dependencies
- **Cargo.toml** - Rust dependencies
- **go.mod** - Go modules
- **requirements.txt** - Python dependencies
- **Dockerfile** - Container security
- **.env** files - Environment variable security

## Security Checks

### Smart Contract Security
- **Reentrancy Attacks**: Detect and prevent reentrancy vulnerabilities
- **Integer Overflow/Underflow**: SafeMath usage and arithmetic checks
- **Access Control**: Proper permission and role management
- **Gas Optimization**: Efficient gas usage patterns
- **External Calls**: Safe interaction with external contracts

### General Security
- **Private Key Exposure**: Detect hardcoded private keys and mnemonics
- **Environment Variables**: Secure handling of sensitive configuration
- **Dependency Vulnerabilities**: Known security issues in dependencies
- **Code Quality**: Security-focused code quality checks

### Blockchain-Specific
- **Ethereum**: EVM-specific security patterns and anti-patterns
- **Bitcoin**: Script validation and UTXO security
- **Multi-chain**: Cross-chain security considerations

## Troubleshooting

### Common Issues

**Extension not connecting to SPT backend**
- Verify the backend server is running on the configured URL
- Check firewall settings and network connectivity
- Ensure the API key is correctly configured

**No security issues detected**
- Verify the file types are supported
- Check if the minimum severity level is appropriate
- Ensure the selected blockchain chains match your project

**Performance issues**
- Disable auto-scan for large projects
- Adjust the scan scope in project settings
- Consider using file-specific scanning instead of project-wide

### Debug Mode
Enable debug logging in VSCode settings:
```json
{
  "spt.debug": true
}
```

## Development & Deployment

### Development Setup
```bash
# Clone the repository
git clone https://github.com/blockchain-spt/vscode-extension
cd vscode-extension

# Install dependencies
npm install

# Compile TypeScript
npm run compile

# Run tests
npm test

# Package extension
npm run package
```

### Building and Packaging
```bash
# Build for production
npm run vscode:prepublish

# Create VSIX package
npm run package

# Install locally for testing
code --install-extension blockchain-security-protocol-1.0.0.vsix
```

### Publishing
For detailed deployment and publishing instructions, see our [VSCode Extension Deployment Guide](../docs/vscode-extension-deployment.md).

Quick publish commands:
```bash
# Install vsce globally
npm install -g @vscode/vsce

# Login to marketplace
vsce login <publisher-name>

# Publish to VS Code Marketplace
npm run publish
```

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- **Documentation**: [https://docs.blockchain-spt.com](https://docs.blockchain-spt.com)
- **Issues**: [GitHub Issues](https://github.com/blockchain-spt/vscode-extension/issues)
- **Discord**: [Join our community](https://discord.gg/blockchain-spt)
- **Email**: <EMAIL>

---

**Made with ❤️ by the Blockchain Security Protocol Team**
