package ethereum

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/config"
	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
)

// Scanner represents the Ethereum security scanner
type Scanner struct {
	config                *config.Config
	logger                *logrus.Logger
	vulnerabilityDetector *VulnerabilityDetector
	gasAnalyzer           *GasAnalyzer
	astParser             *ASTParser
}

// NewScanner creates a new Ethereum scanner instance
func NewScanner(cfg *config.Config) (*Scanner, error) {
	return &Scanner{
		config:                cfg,
		logger:                logrus.New(),
		vulnerabilityDetector: NewVulnerabilityDetector(),
		gasAnalyzer:           NewGasAnalyzer(),
		astParser:             NewASTParser(),
	}, nil
}

// ScanProject scans an entire project for Ethereum-specific security issues
func (s *Scanner) ScanProject(ctx context.Context, projectPath string) ([]models.SecurityIssue, error) {
	s.logger.Infof("Starting Ethereum security scan for project: %s", projectPath)

	var allIssues []models.SecurityIssue

	// Find all Solidity files
	solidityFiles, err := s.findSolidityFiles(projectPath)
	if err != nil {
		return nil, fmt.Errorf("failed to find Solidity files: %w", err)
	}

	// Scan each Solidity file
	for _, file := range solidityFiles {
		issues, err := s.ScanFile(ctx, file)
		if err != nil {
			s.logger.Errorf("Failed to scan file %s: %v", file, err)
			continue
		}
		allIssues = append(allIssues, issues...)
	}

	s.logger.Infof("Ethereum scan completed, found %d issues", len(allIssues))
	return allIssues, nil
}

// ScanFile scans a specific file for Ethereum security issues
func (s *Scanner) ScanFile(ctx context.Context, filePath string) ([]models.SecurityIssue, error) {
	if !strings.HasSuffix(filePath, ".sol") {
		return nil, nil // Not a Solidity file
	}

	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	var issues []models.SecurityIssue

	// Use advanced vulnerability detection
	vulnIssues, err := s.vulnerabilityDetector.DetectVulnerabilities(filePath, string(content))
	if err != nil {
		s.logger.Warnf("Advanced vulnerability detection failed, using fallback: %v", err)
		// Fallback to basic checks
		issues = append(issues, s.checkReentrancy(filePath, string(content))...)
		issues = append(issues, s.checkIntegerOverflow(filePath, string(content))...)
		issues = append(issues, s.checkUncheckedCalls(filePath, string(content))...)
		issues = append(issues, s.checkAccessControl(filePath, string(content))...)
		issues = append(issues, s.checkDeprecatedFunctions(filePath, string(content))...)
	} else {
		issues = append(issues, vulnIssues...)
	}

	// Parse AST for gas analysis
	ast, err := s.astParser.ParseContract(filePath)
	if err != nil {
		s.logger.Warnf("AST parsing failed for gas analysis: %v", err)
		// Fallback to basic gas optimization checks
		issues = append(issues, s.checkGasOptimization(filePath, string(content))...)
	} else {
		// Use advanced gas analysis
		gasIssues, err := s.gasAnalyzer.AnalyzeGasOptimizations(filePath, string(content), ast)
		if err != nil {
			s.logger.Warnf("Gas analysis failed: %v", err)
			// Fallback to basic gas optimization checks
			issues = append(issues, s.checkGasOptimization(filePath, string(content))...)
		} else {
			issues = append(issues, gasIssues...)
		}
	}

	return issues, nil
}

// checkReentrancy checks for reentrancy vulnerabilities
func (s *Scanner) checkReentrancy(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Pattern for external calls followed by state changes
	externalCallPattern := regexp.MustCompile(`(?i)(\.call\(|\.send\(|\.transfer\()`)
	stateChangePattern := regexp.MustCompile(`(?i)(balance\s*=|balances\[.*\]\s*=|\w+\s*=)`)

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if externalCallPattern.MatchString(line) {
			// Check subsequent lines for state changes
			for j := i + 1; j < len(lines) && j < i+10; j++ {
				if stateChangePattern.MatchString(lines[j]) {
					externalCall := strings.TrimSpace(line)
					stateChange := strings.TrimSpace(lines[j])

					issues = append(issues, models.SecurityIssue{
						ID:          fmt.Sprintf("reentrancy_%s_%d", filepath.Base(filePath), i+1),
						Type:        "reentrancy",
						Severity:    "high",
						Title:       "Reentrancy Vulnerability: External Call Before State Change",
						Description: fmt.Sprintf("Potential reentrancy attack detected. External call '%s' on line %d is followed by state change '%s' on line %d. This allows malicious contracts to re-enter and manipulate state.", externalCall, i+1, stateChange, j+1),
						File:        filePath,
						Line:        i + 1,
						Code:        externalCall,
						Chain:       "ethereum",
						Category:    "smart_contract",
						CWE:         "CWE-362",
						OWASP:       "A06:2021 – Vulnerable and Outdated Components",
						Suggestion:  fmt.Sprintf("Fix this reentrancy vulnerability by: 1) Moving state changes before external calls (checks-effects-interactions pattern), 2) Using OpenZeppelin's ReentrancyGuard modifier, or 3) Using 'transfer()' instead of 'call()' for simple Ether transfers. Current pattern: Line %d: %s → Line %d: %s", i+1, externalCall, j+1, stateChange),
						References: []string{
							"https://consensys.github.io/smart-contract-best-practices/attacks/reentrancy/",
							"https://docs.openzeppelin.com/contracts/4.x/api/security#ReentrancyGuard",
							"https://solidity-by-example.org/hacks/re-entrancy/",
						},
						CreatedAt: time.Now(),
					})
					break
				}
			}
		}
	}

	return issues
}

// checkIntegerOverflow checks for integer overflow vulnerabilities
func (s *Scanner) checkIntegerOverflow(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Pattern for arithmetic operations without SafeMath
	arithmeticPattern := regexp.MustCompile(`(?i)(\w+\s*[\+\-\*\/]\s*\w+|\w+\s*[\+\-]\+|\w+\s*\*\*\s*\w+)`)
	safeMathPattern := regexp.MustCompile(`(?i)(SafeMath|using\s+SafeMath)`)

	// Check if SafeMath is used
	usesSafeMath := safeMathPattern.MatchString(content)

	if !usesSafeMath {
		lines := strings.Split(content, "\n")
		for i, line := range lines {
			if arithmeticPattern.MatchString(line) && !strings.Contains(line, "//") {
				operation := strings.TrimSpace(line)
				operationType := s.getArithmeticOperationType(operation)

				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("overflow_%s_%d", filepath.Base(filePath), i+1),
					Type:        "integer_overflow",
					Severity:    "medium",
					Title:       fmt.Sprintf("Integer Overflow Risk: %s Operation Without Protection", operationType),
					Description: fmt.Sprintf("Arithmetic operation '%s' detected without overflow protection. This %s operation could overflow/underflow, leading to unexpected behavior or security vulnerabilities. The contract does not use SafeMath library or Solidity 0.8+ built-in protection.", operation, strings.ToLower(operationType)),
					File:        filePath,
					Line:        i + 1,
					Code:        operation,
					Chain:       "ethereum",
					Category:    "smart_contract",
					CWE:         "CWE-190",
					Suggestion:  fmt.Sprintf("Protect against integer overflow/underflow by: 1) Upgrading to Solidity 0.8+ which has built-in overflow protection, 2) Using OpenZeppelin's SafeMath library for older versions, or 3) Adding manual checks before arithmetic operations. For '%s': Consider using SafeMath.%s() or upgrading compiler version.", operation, s.getSafeMathMethod(operationType)),
					References: []string{
						"https://docs.openzeppelin.com/contracts/2.x/api/math#SafeMath",
						"https://consensys.github.io/smart-contract-best-practices/attacks/integer-overflow-and-underflow/",
						"https://blog.soliditylang.org/2020/10/28/solidity-0.8.x-preview/",
					},
					CreatedAt: time.Now(),
				})
			}
		}
	}

	return issues
}

// checkUncheckedCalls checks for unchecked external calls
func (s *Scanner) checkUncheckedCalls(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Pattern for external calls without return value checking
	uncheckedCallPattern := regexp.MustCompile(`(?i)(\w+\.call\(|\w+\.send\()`)

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if uncheckedCallPattern.MatchString(line) && !strings.Contains(line, "require(") && !strings.Contains(line, "assert(") {
			callCode := strings.TrimSpace(line)
			callType := s.getCallType(callCode)

			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("unchecked_call_%s_%d", filepath.Base(filePath), i+1),
				Type:        "unchecked_call",
				Severity:    "medium",
				Title:       fmt.Sprintf("Unchecked %s Call Return Value", callType),
				Description: fmt.Sprintf("External %s call '%s' return value is not checked. %s calls can fail silently, and unchecked failures can lead to unexpected contract behavior, loss of funds, or security vulnerabilities.", strings.ToLower(callType), callCode, callType),
				File:        filePath,
				Line:        i + 1,
				Code:        callCode,
				Chain:       "ethereum",
				Category:    "smart_contract",
				CWE:         "CWE-252",
				Suggestion:  fmt.Sprintf("Always check the return value of %s calls. Use: 'require(%s, \"Call failed\");' or '(bool success, ) = %s; require(success, \"Call failed\");'. For %s specifically: %s", strings.ToLower(callType), callCode, callCode, strings.ToLower(callType), s.getCallSpecificSuggestion(callType)),
				References: []string{
					"https://consensys.github.io/smart-contract-best-practices/recommendations/#handle-errors-in-external-calls",
					"https://solidity-by-example.org/call/",
					"https://docs.soliditylang.org/en/latest/control-structures.html#error-handling-assert-require-revert-and-exceptions",
				},
				CreatedAt: time.Now(),
			})
		}
	}

	return issues
}

// checkAccessControl checks for access control issues
func (s *Scanner) checkAccessControl(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Pattern for functions without access modifiers
	functionPattern := regexp.MustCompile(`(?i)function\s+\w+\s*\([^)]*\)\s*(?:public|external)`)
	modifierPattern := regexp.MustCompile(`(?i)(onlyOwner|onlyAdmin|require\(msg\.sender)`)

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if functionPattern.MatchString(line) && !modifierPattern.MatchString(line) {
			// Check if it's a state-changing function
			if strings.Contains(line, "public") || strings.Contains(line, "external") {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("access_control_%s_%d", filepath.Base(filePath), i+1),
					Type:        "access_control",
					Severity:    "medium",
					Title:       "Missing Access Control",
					Description: "Public/external function without access control",
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "ethereum",
					Category:    "smart_contract",
					CWE:         "CWE-284",
					Suggestion:  "Add appropriate access control modifiers (onlyOwner, onlyAdmin, etc.)",
					References:  []string{"https://docs.openzeppelin.com/contracts/4.x/access-control"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkGasOptimization checks for gas optimization opportunities
func (s *Scanner) checkGasOptimization(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Pattern for inefficient storage operations
	storagePattern := regexp.MustCompile(`(?i)(storage\s+\w+\s*=|sstore)`)

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if storagePattern.MatchString(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("gas_optimization_%s_%d", filepath.Base(filePath), i+1),
				Type:        "gas_optimization",
				Severity:    "low",
				Title:       "Gas Optimization Opportunity",
				Description: "Potential gas optimization in storage operations",
				File:        filePath,
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Chain:       "ethereum",
				Category:    "smart_contract",
				Suggestion:  "Consider using memory instead of storage where possible",
				References:  []string{"https://docs.soliditylang.org/en/latest/internals/layout_in_storage.html"},
				CreatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// checkDeprecatedFunctions checks for deprecated Solidity functions
func (s *Scanner) checkDeprecatedFunctions(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	deprecatedFunctions := []string{
		"suicide", "sha3", "block.blockhash", "msg.gas", "throw",
	}

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		for _, deprecated := range deprecatedFunctions {
			if strings.Contains(line, deprecated) {
				lineCode := strings.TrimSpace(line)
				modernAlternative := s.getModernAlternative(deprecated)

				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("deprecated_%s_%d", filepath.Base(filePath), i+1),
					Type:        "deprecated_function",
					Severity:    "medium",
					Title:       fmt.Sprintf("Deprecated Solidity Function: %s", deprecated),
					Description: fmt.Sprintf("Deprecated function '%s' found in code '%s'. This function has been deprecated in newer Solidity versions and may be removed in future releases, potentially causing compilation errors or unexpected behavior.", deprecated, lineCode),
					File:        filePath,
					Line:        i + 1,
					Code:        lineCode,
					Chain:       "ethereum",
					Category:    "smart_contract",
					Suggestion:  fmt.Sprintf("Replace deprecated '%s' with modern alternative: %s. Update your code to use the recommended replacement for better compatibility and security.", deprecated, modernAlternative),
					References: []string{
						"https://docs.soliditylang.org/en/latest/",
						"https://docs.soliditylang.org/en/latest/050-breaking-changes.html",
						"https://blog.soliditylang.org/category/breaking-changes/",
					},
					CreatedAt: time.Now(),
				})
			}
		}
	}

	return issues
}

// findSolidityFiles finds all Solidity files in the project
func (s *Scanner) findSolidityFiles(projectPath string) ([]string, error) {
	var files []string

	err := filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip entire directories that should be excluded
		if info.IsDir() {
			for _, excludePath := range s.config.Scanning.Paths.Exclude {
				if info.Name() == excludePath || strings.Contains(path, excludePath) {
					s.logger.Debugf("Skipping directory: %s", path)
					return filepath.SkipDir
				}
			}

			// Skip scanner source code directories to avoid false positives
			if s.isScannerSourcePath(path) {
				s.logger.Debugf("Skipping scanner source directory: %s", path)
				return filepath.SkipDir
			}

			return nil
		}

		// Skip hidden files
		if strings.HasPrefix(info.Name(), ".") {
			return nil
		}

		if strings.HasSuffix(path, ".sol") {
			// Check if path should be excluded
			for _, excludePath := range s.config.Scanning.Paths.Exclude {
				if strings.Contains(path, excludePath) {
					return nil
				}
			}
			files = append(files, path)
		}

		return nil
	})

	return files, err
}

// isScannerSourcePath checks if a path is part of the scanner source code
func (s *Scanner) isScannerSourcePath(path string) bool {
	scannerPaths := []string{
		"backend/pkg/bitcoin",
		"backend/pkg/ethereum",
		"backend/pkg/security",
		"backend/pkg/dependencies",
		"backend/pkg/scanner",
		"backend/pkg/models",
		"backend/pkg/api",
		"backend/pkg/database",
		"backend/cmd",
		"backend/internal",
	}

	for _, scannerPath := range scannerPaths {
		if strings.Contains(path, scannerPath) {
			return true
		}
	}

	return false
}

// getArithmeticOperationType determines the type of arithmetic operation
func (s *Scanner) getArithmeticOperationType(operation string) string {
	if strings.Contains(operation, "+") || strings.Contains(operation, "++") {
		return "Addition"
	} else if strings.Contains(operation, "-") || strings.Contains(operation, "--") {
		return "Subtraction"
	} else if strings.Contains(operation, "*") {
		return "Multiplication"
	} else if strings.Contains(operation, "/") {
		return "Division"
	} else if strings.Contains(operation, "**") {
		return "Exponentiation"
	} else if strings.Contains(operation, "%") {
		return "Modulo"
	}
	return "Arithmetic"
}

// getSafeMathMethod returns the corresponding SafeMath method for an operation type
func (s *Scanner) getSafeMathMethod(operationType string) string {
	switch operationType {
	case "Addition":
		return "add"
	case "Subtraction":
		return "sub"
	case "Multiplication":
		return "mul"
	case "Division":
		return "div"
	case "Modulo":
		return "mod"
	default:
		return "safeOperation"
	}
}

// getCallType determines the type of external call
func (s *Scanner) getCallType(callCode string) string {
	if strings.Contains(callCode, ".call(") {
		return "Call"
	} else if strings.Contains(callCode, ".send(") {
		return "Send"
	} else if strings.Contains(callCode, ".transfer(") {
		return "Transfer"
	}
	return "External Call"
}

// getCallSpecificSuggestion returns specific suggestions for different call types
func (s *Scanner) getCallSpecificSuggestion(callType string) string {
	switch callType {
	case "Call":
		return "call() is the most flexible but also most dangerous. Always check return value and consider gas limits."
	case "Send":
		return "send() is deprecated. Use transfer() for simple Ether transfers or call() with proper error handling."
	case "Transfer":
		return "transfer() automatically reverts on failure, but has a 2300 gas limit. Consider using call() for more complex operations."
	default:
		return "Always handle potential failures in external calls."
	}
}

// getModernAlternative returns the modern alternative for deprecated functions
func (s *Scanner) getModernAlternative(deprecated string) string {
	switch deprecated {
	case "suicide":
		return "selfdestruct(address) - Use selfdestruct() instead of suicide()"
	case "sha3":
		return "keccak256(bytes) - Use keccak256() instead of sha3()"
	case "block.blockhash":
		return "blockhash(uint) - Use blockhash() instead of block.blockhash()"
	case "msg.gas":
		return "gasleft() - Use gasleft() instead of msg.gas"
	case "throw":
		return "revert() or require() - Use revert() or require() instead of throw"
	default:
		return "Check Solidity documentation for the modern equivalent"
	}
}
