<div class="chart-container">
  <!-- Chart Header -->
  <div class="chart-header">
    <div class="chart-title-section">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="chart-subtitle" *ngIf="data.length > 0">
        Total Issues: {{ getTotalValue() }}
      </div>
    </div>
    
    <div class="chart-controls">
      <!-- Time Range Selector -->
      <mat-form-field appearance="outline" class="time-range-select">
        <mat-select 
          [(value)]="selectedTimeRange" 
          (selectionChange)="onTimeRangeChange($event.value)">
          <mat-option *ngFor="let range of timeRanges" [value]="range.value">
            {{ range.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <!-- Chart Actions -->
      <div class="chart-actions">
        <button 
          mat-icon-button 
          matTooltip="Change Chart Type"
          (click)="toggleChartType()"
          class="action-btn">
          <mat-icon>bar_chart</mat-icon>
        </button>
        
        <button 
          mat-icon-button 
          matTooltip="Refresh Data"
          (click)="refreshData()"
          class="action-btn"
          [disabled]="isLoading">
          <mat-icon [class.spinning]="isLoading">refresh</mat-icon>
        </button>
        
        <button 
          mat-icon-button 
          matTooltip="Export Chart"
          (click)="exportChart()"
          class="action-btn">
          <mat-icon>download</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Chart Content -->
  <div class="chart-content" [style.height.px]="height">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="chart-loading">
      <div class="loading-spinner">
        <mat-icon class="spinning">refresh</mat-icon>
      </div>
      <p>Loading chart data...</p>
    </div>

    <!-- Empty State -->
    <div *ngIf="!isLoading && data.length === 0" class="chart-empty">
      <mat-icon class="empty-icon">assessment</mat-icon>
      <h4>No Data Available</h4>
      <p>No security metrics data to display for the selected time range.</p>
      <button mat-raised-button color="primary" (click)="refreshData()">
        <mat-icon>refresh</mat-icon>
        Refresh Data
      </button>
    </div>

    <!-- Chart Canvas -->
    <div *ngIf="!isLoading && data.length > 0" class="chart-wrapper">
      <canvas 
        #chartCanvas
        class="chart-canvas"
        [attr.aria-label]="title + ' chart'">
      </canvas>
    </div>
  </div>

  <!-- Chart Legend (Custom for trends) -->
  <div *ngIf="showTrends && data.length > 0 && !isLoading" class="chart-trends">
    <div class="trends-header">
      <mat-icon>trending_up</mat-icon>
      <span>Trends</span>
    </div>
    
    <div class="trends-list">
      <div 
        *ngFor="let item of data" 
        class="trend-item"
        [class]="'severity-' + item.severity">
        
        <div class="trend-indicator" [style.background-color]="severityColors[item.severity]"></div>
        
        <div class="trend-content">
          <span class="trend-label">{{ item.label }}</span>
          <div class="trend-value">
            <span class="value">{{ item.value }}</span>
            <span 
              *ngIf="item.trend !== undefined" 
              class="trend-change"
              [class.positive]="item.trend > 0"
              [class.negative]="item.trend < 0"
              [class.neutral]="item.trend === 0">
              
              <mat-icon class="trend-icon">
                {{ item.trend > 0 ? 'trending_up' : item.trend < 0 ? 'trending_down' : 'trending_flat' }}
              </mat-icon>
              <span>{{ Math.abs(item.trend) }}%</span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Chart Summary -->
  <div *ngIf="data.length > 0 && !isLoading" class="chart-summary">
    <div class="summary-stats">
      <div class="summary-item critical" *ngIf="getCriticalCount() > 0">
        <mat-icon>error</mat-icon>
        <span class="count">{{ getCriticalCount() }}</span>
        <span class="label">Critical</span>
      </div>
      
      <div class="summary-item high" *ngIf="getHighCount() > 0">
        <mat-icon>warning</mat-icon>
        <span class="count">{{ getHighCount() }}</span>
        <span class="label">High</span>
      </div>
      
      <div class="summary-item medium" *ngIf="getMediumCount() > 0">
        <mat-icon>info</mat-icon>
        <span class="count">{{ getMediumCount() }}</span>
        <span class="label">Medium</span>
      </div>
      
      <div class="summary-item low" *ngIf="getLowCount() > 0">
        <mat-icon>check_circle</mat-icon>
        <span class="count">{{ getLowCount() }}</span>
        <span class="label">Low</span>
      </div>
    </div>
    
    <div class="summary-actions">
      <button mat-button color="primary" class="view-details-btn">
        <mat-icon>visibility</mat-icon>
        View Details
      </button>
    </div>
  </div>
</div>
