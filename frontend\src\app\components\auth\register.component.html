<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <div class="logo">
        <mat-icon>security</mat-icon>
        <span>SPT</span>
      </div>
      <h1>Create Account</h1>
      <p>Join the SPT Security Platform</p>
    </div>

    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="auth-form">
      <mat-form-field appearance="outline">
        <mat-label>Email</mat-label>
        <input matInput type="email" formControlName="email" autocomplete="email">
        <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
          Email is required
        </mat-error>
        <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
          Please enter a valid email
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Username</mat-label>
        <input matInput formControlName="username" autocomplete="username">
        <mat-error *ngIf="registerForm.get('username')?.hasError('required')">
          Username is required
        </mat-error>
        <mat-error *ngIf="registerForm.get('username')?.hasError('minlength')">
          Username must be at least 3 characters
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Password</mat-label>
        <input matInput
               [type]="hidePassword ? 'password' : 'text'"
               formControlName="password"
               autocomplete="new-password">
        <button mat-icon-button matSuffix
                (click)="hidePassword = !hidePassword"
                type="button">
          <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
        </button>
        <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
          Password is required
        </mat-error>
        <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
          Password must be at least 8 characters
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Confirm Password</mat-label>
        <input matInput
               [type]="hideConfirmPassword ? 'password' : 'text'"
               formControlName="confirmPassword"
               autocomplete="new-password">
        <button mat-icon-button matSuffix
                (click)="hideConfirmPassword = !hideConfirmPassword"
                type="button">
          <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
        </button>
        <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required')">
          Please confirm your password
        </mat-error>
        <mat-error *ngIf="registerForm.hasError('passwordMismatch') && registerForm.get('confirmPassword')?.touched">
          Passwords do not match
        </mat-error>
      </mat-form-field>

      <mat-checkbox formControlName="agreeToTerms" class="terms-checkbox">
        I agree to the <a href="#" class="terms-link">Terms of Service</a> and <a href="#" class="terms-link">Privacy Policy</a>
      </mat-checkbox>

      <button mat-raised-button
              color="primary"
              type="submit"
              class="auth-button"
              [disabled]="registerForm.invalid || isLoading">
        <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
        <span *ngIf="!isLoading">Create Account</span>
      </button>
    </form>

    <div class="auth-footer">
      <p>Already have an account? <a routerLink="/login">Sign in</a></p>
    </div>
  </div>


