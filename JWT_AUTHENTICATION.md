# JWT Authentication

The SPT (Blockchain Security Protocol Tool) uses <PERSON>W<PERSON> (JSON Web Tokens) for secure authentication across all components including the backend API, frontend web application, and VS Code extension.

## Overview

JWT authentication provides:
- **Stateless authentication**: No server-side session storage required
- **Secure token transmission**: Tokens are signed and can be verified
- **Automatic expiration**: Tokens have built-in expiration times
- **Role-based access**: User roles are embedded in token claims
- **Refresh token support**: Long-lived refresh tokens for seamless user experience

## Authentication Flow

### 1. User Login
```bash
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user-uuid",
    "username": "your_username",
    "email": "<EMAIL>",
    "role": "developer"
  },
  "expires_in": 86400
}
```

### 2. User Registration
```bash
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "new_user",
  "email": "<EMAIL>",
  "password": "secure_password",
  "confirmPassword": "secure_password"
}
```

### 3. Token Refresh
```bash
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## Token Structure

### Access Token Claims
```json
{
  "user_id": "user-uuid",
  "username": "username",
  "role": "developer|admin|manager",
  "iss": "spt-backend",
  "sub": "user-uuid",
  "iat": 1640995200,
  "exp": 1641081600,
  "nbf": 1640995200
}
```

### Token Expiration
- **Access Token**: 24 hours
- **Refresh Token**: 7 days

## Using JWT Tokens

### Frontend (Angular)
The frontend automatically handles JWT tokens through an HTTP interceptor:

```typescript
// Tokens are automatically attached to requests
this.http.get('/api/v1/scan/history').subscribe(data => {
  // Request includes: Authorization: Bearer <token>
});
```

### VS Code Extension
The extension uses JWT tokens obtained through the web authentication flow:

```typescript
// Token is automatically attached to API requests
const scanResults = await apiClient.getScanHistory();
```

### Manual API Requests
```bash
curl -H "Authorization: Bearer <your-jwt-token>" \
     http://localhost:8080/api/v1/scan/history
```

## Security Features

### 1. Environment-based Secret Management
```bash
# Set JWT secret via environment variable
export JWT_SECRET="your-super-secure-secret-key-here"
```

If no environment variable is set, a development default is used (should be changed in production).

### 2. Token Validation
- Signature verification using HMAC SHA-256
- Expiration time validation
- Issuer verification
- Required claims validation (user_id, username, role)

### 3. Automatic Token Refresh
- Frontend automatically refreshes tokens 5 minutes before expiration
- Refresh tokens are used to obtain new access tokens
- Failed refresh attempts result in automatic logout

### 4. Secure Storage
- Frontend: Tokens stored in localStorage
- VS Code Extension: Tokens stored in secure extension storage
- Backend: Only token hashes are processed, never stored

## Error Handling

### 401 Unauthorized Responses
```json
{
  "error": "Invalid or expired JWT token",
  "message": "Please re-authenticate"
}
```

### Token Refresh Failures
```json
{
  "error": "Invalid or expired refresh token"
}
```

## VS Code Extension Authentication

### Authentication Flow
1. User runs a command requiring authentication
2. Extension opens web browser to SPT login page
3. User logs in through web interface
4. Browser redirects back to VS Code with JWT token
5. Extension stores token and uses it for API requests

### Configuration
No manual configuration required - authentication is handled automatically through the web flow.

## Best Practices

### For Developers
1. **Never log JWT tokens** - they contain sensitive information
2. **Use HTTPS in production** - tokens should never be transmitted over HTTP
3. **Set strong JWT secrets** - use environment variables in production
4. **Handle token expiration gracefully** - implement automatic refresh
5. **Validate tokens on every request** - never trust client-side validation

### For Production Deployment
1. Set a strong JWT secret:
   ```bash
   export JWT_SECRET="$(openssl rand -base64 64)"
   ```
2. Use HTTPS for all communications
3. Configure proper CORS settings
4. Monitor for suspicious authentication patterns
5. Implement rate limiting on authentication endpoints

## Troubleshooting

### Common Issues

**"Authorization header required"**
- Ensure the Authorization header is included: `Authorization: Bearer <token>`

**"Invalid or expired JWT token"**
- Token may be expired - try refreshing or re-authenticating
- Check that the JWT secret matches between client and server

**"Invalid token claims"**
- Token may be malformed or from a different system
- Re-authenticate to get a new token

### Debug Mode
Enable debug logging to see authentication details:
```bash
export LOG_LEVEL=debug
```

## Migration from API Keys

If you were previously using API keys, they have been removed in favor of JWT-only authentication. All components now use the secure JWT authentication flow described above.
