import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCheckboxModule } from '@angular/material/checkbox';

import { AuthService, LoginRequest } from '../../services/auth.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatCheckboxModule
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']

})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  hidePassword = true;
  isLoading = false;
  returnUrl = '/dashboard';
  isVSCodeAuth = false;
  vscodeState = '';
  vscodeRedirectUri = '';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.loginForm = this.formBuilder.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required]],
      rememberMe: [false]
    });
  }

  ngOnInit(): void {
    // Check if this is VS Code authentication
    this.route.queryParams.subscribe(params => {
      this.isVSCodeAuth = params['vscode'] === 'true';
      this.vscodeState = params['state'] || '';
      this.vscodeRedirectUri = params['redirect_uri'] || '';

      if (!this.isVSCodeAuth) {
        // Get return URL from route parameters or default to dashboard
        this.returnUrl = params['returnUrl'] || '/dashboard';
      }
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isLoading = true;
      
      const credentials: LoginRequest = {
        username: this.loginForm.value.username,
        password: this.loginForm.value.password
      };

      this.authService.login(credentials).subscribe({
        next: (response) => {
          this.isLoading = false;

          if (this.isVSCodeAuth) {
            // Handle VS Code authentication callback
            this.handleVSCodeCallback(response);
          } else {
            this.snackBar.open('Login successful!', 'Close', { duration: 3000 });
            this.router.navigate([this.returnUrl]);
          }
        },
        error: (error) => {
          this.isLoading = false;
          this.snackBar.open(error.message || 'Login failed', 'Close', { duration: 5000 });
        }
      });
    }
  }

  private handleVSCodeCallback(response: any): void {
    // Construct the VS Code callback URL
    const callbackUrl = new URL(this.vscodeRedirectUri);
    callbackUrl.searchParams.set('token', response.token);
    callbackUrl.searchParams.set('state', this.vscodeState);
    callbackUrl.searchParams.set('user', encodeURIComponent(JSON.stringify(response.user)));
    callbackUrl.searchParams.set('expires_in', response.expires_in.toString());

    // Show success message
    this.snackBar.open('Authentication successful! Redirecting to VS Code...', 'Close', { duration: 3000 });

    // Redirect to VS Code after a short delay
    setTimeout(() => {
      window.location.href = callbackUrl.toString();
    }, 2000);
  }

  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }
}
