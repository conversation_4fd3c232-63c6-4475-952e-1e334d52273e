/* Security Trends Dashboard Component Styles */

.trends-dashboard {
  display: flex;
  flex-direction: column;
  gap: var(--spt-space-6);
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spt-space-6);
  background: var(--spt-surface);
  border-radius: var(--spt-radius-xl);
  border: 1px solid var(--spt-border);
  box-shadow: var(--spt-shadow-sm);
}

.header-content {
  flex: 1;
}

.dashboard-title {
  display: flex;
  align-items: center;
  gap: var(--spt-space-3);
  margin: 0 0 var(--spt-space-2) 0;
  font-size: var(--spt-text-2xl);
  font-weight: var(--spt-font-bold);
  color: var(--spt-text-primary);
}

.dashboard-title mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  color: var(--spt-primary-600);
}

.dashboard-subtitle {
  margin: 0;
  font-size: var(--spt-text-base);
  color: var(--spt-text-secondary);
  line-height: var(--spt-leading-relaxed);
}

.header-controls {
  display: flex;
  align-items: center;
  gap: var(--spt-space-4);
}

.period-select {
  min-width: 160px;
  
  ::ng-deep .mat-mdc-form-field-wrapper {
    padding-bottom: 0;
  }
}

.header-actions {
  display: flex;
  gap: var(--spt-space-2);
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: var(--spt-radius-lg);
  color: var(--spt-text-secondary);
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--spt-primary-50);
  color: var(--spt-primary-600);
  transform: scale(1.05);
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spt-space-4);
}

.summary-card {
  background: var(--spt-surface);
  border-radius: var(--spt-radius-xl);
  border: 1px solid var(--spt-border);
  box-shadow: var(--spt-shadow-sm);
  padding: var(--spt-space-6);
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--spt-shadow-md);
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spt-space-3);
  margin-bottom: var(--spt-space-4);
}

.card-header mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.summary-card.total-issues .card-header mat-icon { color: var(--spt-error-600); }
.summary-card.security-score .card-header mat-icon { color: var(--spt-warning-600); }
.summary-card.scan-frequency .card-header mat-icon { color: var(--spt-info-600); }
.summary-card.trend-indicator .card-header mat-icon { color: var(--spt-success-600); }

.card-title {
  font-size: var(--spt-text-sm);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: var(--spt-space-2);
}

.metric-value {
  font-size: var(--spt-text-3xl);
  font-weight: var(--spt-font-bold);
  color: var(--spt-text-primary);
  line-height: var(--spt-leading-none);
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: var(--spt-space-1);
  font-size: var(--spt-text-sm);
  font-weight: var(--spt-font-semibold);
}

.metric-trend mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.metric-label {
  font-size: var(--spt-text-sm);
  color: var(--spt-text-secondary);
}

.score-bar {
  width: 100%;
  height: 8px;
  background: var(--spt-gray-200);
  border-radius: var(--spt-radius-full);
  overflow: hidden;
}

.score-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--spt-error-500) 0%, var(--spt-warning-500) 50%, var(--spt-success-500) 100%);
  border-radius: var(--spt-radius-full);
  transition: width 0.5s ease;
}

.trend-status {
  display: flex;
  align-items: center;
  gap: var(--spt-space-2);
  font-size: var(--spt-text-base);
  font-weight: var(--spt-font-semibold);
}

.trend-status mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.trend-status.trend-up { color: var(--spt-error-600); }
.trend-status.trend-down { color: var(--spt-success-600); }
.trend-status.trend-flat { color: var(--spt-text-secondary); }

/* Chart Section */
.chart-section {
  background: var(--spt-surface);
  border-radius: var(--spt-radius-xl);
  border: 1px solid var(--spt-border);
  box-shadow: var(--spt-shadow-sm);
  overflow: hidden;
}

.chart-tabs {
  ::ng-deep .mat-mdc-tab-group {
    --mdc-tab-indicator-active-indicator-color: var(--spt-primary-600);
  }
  
  ::ng-deep .mat-mdc-tab {
    min-width: 120px;
  }
  
  ::ng-deep .mat-mdc-tab .mdc-tab__content {
    display: flex;
    align-items: center;
    gap: var(--spt-space-2);
  }
  
  ::ng-deep .mat-mdc-tab-header {
    border-bottom: 1px solid var(--spt-border-light);
  }
}

.chart-container {
  padding: var(--spt-space-6);
}

.chart-header {
  margin-bottom: var(--spt-space-6);
}

.chart-header h3 {
  margin: 0 0 var(--spt-space-2) 0;
  font-size: var(--spt-text-lg);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.chart-header p {
  margin: 0;
  font-size: var(--spt-text-sm);
  color: var(--spt-text-secondary);
  line-height: var(--spt-leading-relaxed);
}

.chart-content {
  position: relative;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-content.loading {
  background: var(--spt-bg-secondary);
  border-radius: var(--spt-radius-lg);
}

.chart-canvas {
  max-width: 100%;
  max-height: 100%;
}

.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spt-space-3);
  color: var(--spt-text-secondary);
}

.chart-loading mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: var(--spt-primary-500);
}

.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: var(--spt-text-secondary);
}

.chart-empty mat-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: var(--spt-text-tertiary);
  margin-bottom: var(--spt-space-4);
}

.chart-empty h4 {
  margin: 0 0 var(--spt-space-2) 0;
  font-size: var(--spt-text-lg);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.chart-empty p {
  margin: 0;
  font-size: var(--spt-text-sm);
  max-width: 300px;
  line-height: var(--spt-leading-relaxed);
}

/* Insights Panel */
.insights-panel {
  background: var(--spt-surface);
  border-radius: var(--spt-radius-xl);
  border: 1px solid var(--spt-border);
  box-shadow: var(--spt-shadow-sm);
  padding: var(--spt-space-6);
}

.insights-header {
  display: flex;
  align-items: center;
  gap: var(--spt-space-3);
  margin-bottom: var(--spt-space-4);
}

.insights-header mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: var(--spt-warning-600);
}

.insights-header h3 {
  margin: 0;
  font-size: var(--spt-text-lg);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.insights-content {
  display: flex;
  flex-direction: column;
  gap: var(--spt-space-4);
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spt-space-3);
  padding: var(--spt-space-4);
  background: var(--spt-bg-secondary);
  border-radius: var(--spt-radius-lg);
  border-left: 4px solid transparent;
}

.insight-item .insight-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  margin-top: var(--spt-space-1);
  flex-shrink: 0;
}

.insight-item .insight-icon.success {
  color: var(--spt-success-600);
}

.insight-item .insight-icon.warning {
  color: var(--spt-warning-600);
}

.insight-item .insight-icon.error {
  color: var(--spt-error-600);
}

.insight-item .insight-icon.info {
  color: var(--spt-info-600);
}

.insight-item:has(.insight-icon.success) {
  border-left-color: var(--spt-success-600);
  background: var(--spt-success-50);
}

.insight-item:has(.insight-icon.warning) {
  border-left-color: var(--spt-warning-600);
  background: var(--spt-warning-50);
}

.insight-item:has(.insight-icon.error) {
  border-left-color: var(--spt-error-600);
  background: var(--spt-error-50);
}

.insight-item:has(.insight-icon.info) {
  border-left-color: var(--spt-info-600);
  background: var(--spt-info-50);
}

.insight-text {
  font-size: var(--spt-text-sm);
  color: var(--spt-text-primary);
  line-height: var(--spt-leading-relaxed);
}

.insight-text strong {
  font-weight: var(--spt-font-semibold);
}

/* Empty Dashboard */
.empty-dashboard {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spt-space-16);
  text-align: center;
  background: var(--spt-surface);
  border-radius: var(--spt-radius-xl);
  border: 1px solid var(--spt-border);
  box-shadow: var(--spt-shadow-sm);
}

.empty-icon {
  font-size: 80px;
  width: 80px;
  height: 80px;
  color: var(--spt-text-tertiary);
  margin-bottom: var(--spt-space-6);
}

.empty-dashboard h3 {
  margin: 0 0 var(--spt-space-3) 0;
  font-size: var(--spt-text-xl);
  font-weight: var(--spt-font-semibold);
  color: var(--spt-text-primary);
}

.empty-dashboard p {
  margin: 0 0 var(--spt-space-8) 0;
  font-size: var(--spt-text-base);
  color: var(--spt-text-secondary);
  max-width: 400px;
  line-height: var(--spt-leading-relaxed);
}

/* Animations */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: var(--spt-space-4);
    align-items: stretch;
  }

  .header-controls {
    justify-content: space-between;
  }

  .summary-cards {
    grid-template-columns: 1fr;
  }

  .chart-content {
    height: 300px;
  }

  .insights-content {
    gap: var(--spt-space-3);
  }

  .insight-item {
    flex-direction: column;
    gap: var(--spt-space-2);
  }
}
